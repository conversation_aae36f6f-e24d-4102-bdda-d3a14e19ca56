<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Import.ImportView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:SurveyPointsManager.Views.Shared"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ImportViewModel}"
    d:DesignHeight="700"
    d:DesignWidth="450"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <DockPanel LastChildFill="True">
        <!--  Shared Header  -->
        <shared:SharedHeaderView DockPanel.Dock="Top" />

        <StatusBar DockPanel.Dock="Bottom">
            <StatusBarItem>
                <TextBlock Text="255 Points" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="Ready" />
            </StatusBarItem>
        </StatusBar>

        <!--  Content  -->
        <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Title  -->
                <TextBlock
                    Grid.Row="0"
                    Margin="0,0,0,16"
                    HorizontalAlignment="Center"
                    FontSize="18"
                    FontWeight="SemiBold"
                    Text="Import Survey Points" />

                <!--  File Selection & Options  -->
                <GroupBox
                    Grid.Row="1"
                    Padding="5"
                    Header="Source File">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBox
                                Grid.Column="0"
                                Margin="0,0,8,0"
                                VerticalContentAlignment="Center"
                                Text="C:\Path\To\Your\SurveyFile.csv" />
                            <Button Grid.Column="1" Padding="10,5">
                                <StackPanel Orientation="Horizontal">
                                    <Path
                                        Width="16"
                                        Height="16"
                                        Margin="0,0,5,0"
                                        Data="{StaticResource Icon.FileImport}"
                                        Style="{StaticResource IconStyle}" />
                                    <TextBlock VerticalAlignment="Center" Text="Browse..." />
                                </StackPanel>
                            </Button>
                        </Grid>
                        <TextBlock Margin="0,8,0,2" Text="File Format:" />
                        <ComboBox SelectedIndex="0">
                            <ComboBoxItem>CSV / TXT</ComboBoxItem>
                            <ComboBoxItem>LandXML (*.xml)</ComboBoxItem>
                            <ComboBoxItem>DXF (*.dxf)</ComboBoxItem>
                            <ComboBoxItem>DWG (*.dwg)</ComboBoxItem>
                            <ComboBoxItem>Shapefile (*.shp)</ComboBoxItem>
                            <ComboBoxItem>KML / KMZ</ComboBoxItem>
                            <ComboBoxItem>GPX (*.gpx)</ComboBoxItem>
                            <ComboBoxItem>GeoJSON (*.geojson)</ComboBoxItem>
                            <ComboBoxItem>SDR (*.sdr)</ComboBoxItem>
                            <ComboBoxItem>IDX (*.idx)</ComboBoxItem>
                            <ComboBoxItem>GSI (*.gsi)</ComboBoxItem>
                        </ComboBox>
                    </StackPanel>
                </GroupBox>

                <Grid Grid.Row="2" Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <GroupBox
                        Grid.Column="0"
                        Padding="5,5,5,2"
                        Header="Import Mode">
                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                            <RadioButton
                                Margin="0,0,10,0"
                                Content="Append"
                                IsChecked="True" />
                            <RadioButton Content="Replace" />
                        </StackPanel>
                    </GroupBox>
                    <GroupBox
                        Grid.Column="1"
                        Margin="5,0,0,0"
                        Padding="5,5,5,2"
                        Header="Format Specific Options">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontStyle="Italic"
                            Foreground="Gray"
                            Text="Options shown here..." />
                    </GroupBox>
                </Grid>

                <!--  Data Preview  -->
                <GroupBox
                    Grid.Row="3"
                    Margin="0,10,0,0"
                    Header="Editable Point Grid">
                    <DockPanel>
                        <WrapPanel Margin="0,0,0,5" DockPanel.Dock="Top">
                            <Button Padding="5,2" ToolTip="Undo">
                                <Path
                                    Width="16"
                                    Height="16"
                                    Data="{StaticResource Icon.Undo}"
                                    Style="{StaticResource IconStyle}" />
                            </Button>
                            <Button
                                Margin="4,0,0,0"
                                Padding="5,2"
                                ToolTip="Redo">
                                <Path
                                    Width="16"
                                    Height="16"
                                    Data="{StaticResource Icon.Redo}"
                                    Style="{StaticResource IconStyle}" />
                            </Button>
                            <Rectangle
                                Width="1"
                                Margin="8,0"
                                Fill="LightGray" />
                            <TextBox
                                Width="150"
                                VerticalContentAlignment="Center"
                                Text="Search..." />
                        </WrapPanel>
                        <DataGrid
                            AutoGenerateColumns="False"
                            CanUserAddRows="True"
                            CanUserDeleteRows="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding PointNumber}"
                                    Header="Point #" />
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding Northing}"
                                    Header="Northing" />
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding Easting}"
                                    Header="Easting" />
                                <DataGridTextColumn
                                    Width="*"
                                    Binding="{Binding Elevation}"
                                    Header="Elevation" />
                                <DataGridTextColumn
                                    Width="2*"
                                    Binding="{Binding Description}"
                                    Header="Description" />
                            </DataGrid.Columns>
                        </DataGrid>
                    </DockPanel>
                </GroupBox>

                <!--  Import Actions  -->
                <Button
                    Grid.Row="4"
                    Margin="0,15,0,0"
                    Padding="10,8"
                    HorizontalAlignment="Center">
                    <StackPanel Orientation="Horizontal">
                        <Path
                            Width="20"
                            Height="20"
                            Margin="0,0,8,0"
                            Data="{StaticResource Icon.Import}"
                            Style="{StaticResource IconStyle}" />
                        <TextBlock
                            VerticalAlignment="Center"
                            FontSize="14"
                            Text="Import Points" />
                    </StackPanel>
                </Button>
            </Grid>
        </ScrollViewer>
    </DockPanel>
</UserControl> 