<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Import.ImportView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:SurveyPointsManager.Views.Shared"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    xmlns:import="clr-namespace:SurveyPointsManager.Views.Import"
    d:DataContext="{d:DesignInstance Type=vm:ImportViewModel}"
    d:DesignHeight="700"
    d:DesignWidth="450"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter for boolean to visibility -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <DockPanel LastChildFill="True">
        <!--  Shared Header  -->
        <shared:SharedHeaderView DockPanel.Dock="Top" />

        <!-- Dynamic Status Bar -->
        <StatusBar DockPanel.Dock="Bottom">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusText}" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="{Binding ProcessingMessage}" />
            </StatusBarItem>
        </StatusBar>

        <!-- Main Content Area with View Switching -->
        <Grid>
            <!-- Initial View - Blank slate with [+] Select Files button -->
            <import:InitialView
                DataContext="{Binding}"
                Visibility="{Binding IsInitialViewVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />

            <!-- Staging View - Configuration with staged files and settings -->
            <import:StagingView
                DataContext="{Binding}"
                Visibility="{Binding IsStagingViewVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />

            <!-- Processing View - Background processing with progress bar -->
            <import:ProcessingView
                DataContext="{Binding}"
                Visibility="{Binding IsProcessingViewVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />

            <!-- Data Review View - Interactive data grid workspace -->
            <import:DataReviewView
                DataContext="{Binding}"
                Visibility="{Binding IsDataReviewViewVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
        </Grid>
    </DockPanel>
</UserControl>

