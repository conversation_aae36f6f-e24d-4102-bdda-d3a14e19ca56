using SurveyPointsManager.Infrastructure.Base;
using SurveyPointsManager.Infrastructure.Commands;
using SurveyPointsManager.Infrastructure.Database;
using System.Windows.Input;

namespace SurveyPointsManager.ViewModels
{
    public class SettingsViewModel : ViewModelBase
    {
        private string _title = "Settings";
        
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public ICommand AddTestDataCommand { get; }

        public SettingsViewModel()
        {
            AddTestDataCommand = new RelayCommand(AddTestData);
        }

        private void AddTestData(object parameter)
        {
            var dbService = new DatabaseService();
            dbService.InitializeDatabase();
            dbService.InsertTestPointCollection();
        }
    }
} 