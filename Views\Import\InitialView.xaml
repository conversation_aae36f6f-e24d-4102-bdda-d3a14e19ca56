<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Import.InitialView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ImportViewModel}"
    d:DesignHeight="500"
    d:DesignWidth="400"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Style for the main select files button -->
            <Style x:Key="SelectFilesButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                <Setter Property="BorderBrush" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                <Setter Property="BorderThickness" Value="2" />
                <Setter Property="Padding" Value="20,15" />
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                                <ContentPresenter
                                    Margin="{TemplateBinding Padding}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.HighlightBrushKey}" />
                                    <Setter Property="Foreground" Value="{DynamicResource SystemColors.HighlightTextBrushKey}" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Style for the instruction text -->
            <Style x:Key="InstructionTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Foreground" Value="{DynamicResource SystemColors.GrayTextBrushKey}" />
                <Setter Property="TextAlignment" Value="Center" />
                <Setter Property="TextWrapping" Value="Wrap" />
                <Setter Property="LineHeight" Value="20" />
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <!-- Main content area -->
        <StackPanel
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Orientation="Vertical">

            <!-- Large plus icon -->
            <Border
                Width="80"
                Height="80"
                Margin="0,0,0,20"
                Background="{DynamicResource SystemColors.ControlLightBrushKey}"
                BorderBrush="{DynamicResource SystemColors.ControlDarkBrushKey}"
                BorderThickness="2"
                CornerRadius="40">
                <Path
                    Width="40"
                    Height="40"
                    Data="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"
                    Fill="{DynamicResource SystemColors.ControlTextBrushKey}"
                    Stretch="Uniform" />
            </Border>

            <!-- Main instruction -->
            <TextBlock
                Margin="0,0,0,10"
                FontSize="18"
                FontWeight="SemiBold"
                Text="Select Files to Import"
                TextAlignment="Center" />

            <!-- Detailed instructions -->
            <TextBlock
                Margin="0,0,0,25"
                Style="{StaticResource InstructionTextStyle}">
                <Run Text="Choose one or more survey files to import." />
                <LineBreak />
                <Run Text="Supported formats: CSV, TXT, LandXML, DXF, DWG," />
                <LineBreak />
                <Run Text="Shapefile, KML, GPX, GeoJSON, SDR, IDX, GSI" />
            </TextBlock>

            <!-- Select Files Button -->
            <Button
                Command="{Binding SelectFilesCommand}"
                Style="{StaticResource SelectFilesButtonStyle}"
                ToolTip="Browse and select survey files for import">
                <StackPanel Orientation="Horizontal">
                    <Path
                        Width="24"
                        Height="24"
                        Margin="0,0,10,0"
                        Data="{StaticResource Icon.FileImport}"
                        Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                        Stretch="Uniform" />
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="16"
                        FontWeight="SemiBold"
                        Text="Select Files" />
                </StackPanel>
            </Button>

            <!-- Supported formats hint -->
            <TextBlock
                Margin="0,20,0,0"
                FontSize="12"
                Foreground="{DynamicResource SystemColors.GrayTextBrushKey}"
                Text="Multiple files can be selected at once"
                TextAlignment="Center" />
        </StackPanel>

        <!-- Status footer -->
        <Border
            Height="30"
            Padding="10,5"
            VerticalAlignment="Bottom"
            Background="{DynamicResource SystemColors.ControlBrushKey}"
            BorderBrush="{DynamicResource SystemColors.ControlDarkBrushKey}"
            BorderThickness="0,1,0,0">
            <TextBlock
                VerticalAlignment="Center"
                FontSize="12"
                Text="{Binding StatusText}" />
        </Border>
    </Grid>
</UserControl>
