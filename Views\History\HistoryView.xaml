<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.History.HistoryView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:SurveyPointsManager.Views.Shared"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:HistoryViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="450"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <DockPanel LastChildFill="True">
        <!--  Shared Header  -->
        <shared:SharedHeaderView DockPanel.Dock="Top" />

        <StatusBar DockPanel.Dock="Bottom">
            <StatusBarItem>
                <TextBlock Text="Total Records: 1,234" />
            </StatusBarItem>
        </StatusBar>

        <!--  Content  -->
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <TextBlock
                Grid.Row="0"
                Margin="0,0,0,16"
                HorizontalAlignment="Center"
                FontSize="18"
                FontWeight="SemiBold"
                Text="Action History" />

            <DockPanel Grid.Row="1">
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <Button Padding="5,2" ToolTip="Undo last operation">
                        <StackPanel Orientation="Horizontal">
                            <Path
                                Width="14"
                                Height="14"
                                Margin="0,0,4,0"
                                Data="{StaticResource Icon.Undo}"
                                Style="{StaticResource IconStyle}" />
                            <TextBlock Text="Undo" />
                        </StackPanel>
                    </Button>
                    <Button
                        Margin="5,0,0,0"
                        Padding="5,2"
                        ToolTip="Delete selected log entry">
                        <StackPanel Orientation="Horizontal">
                            <Path
                                Width="14"
                                Height="14"
                                Margin="0,0,4,0"
                                Data="{StaticResource Icon.Delete}"
                                Style="{StaticResource IconStyle}" />
                            <TextBlock Text="Delete" />
                        </StackPanel>
                    </Button>
                </StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <TextBox VerticalContentAlignment="Center" Text="Search log..." />
                    <Path
                        Grid.Column="1"
                        Width="16"
                        Height="16"
                        Margin="5,0"
                        VerticalAlignment="Center"
                        Data="{StaticResource Icon.Search}"
                        Fill="Gray"
                        Style="{StaticResource IconStyle}" />
                </Grid>
            </DockPanel>

            <DataGrid
                Grid.Row="2"
                Margin="0,10,0,0"
                AutoGenerateColumns="False"
                IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Timestamp}"
                        Header="Timestamp" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Operation}"
                        Header="Operation" />
                    <DataGridTextColumn
                        Width="Auto"
                        Binding="{Binding Format}"
                        Header="Format" />
                    <DataGridTextColumn
                        Width="Auto"
                        Binding="{Binding PointCount}"
                        Header="Point Count" />
                    <DataGridTextColumn
                        Width="2*"
                        Binding="{Binding FileName}"
                        Header="File Name" />
                </DataGrid.Columns>
            </DataGrid>

            <GroupBox
                Grid.Row="3"
                Margin="0,10,0,0"
                Padding="5"
                Header="Retention Settings">
                <StackPanel Orientation="Horizontal">
                    <TextBlock VerticalAlignment="Center" Text="Max Records to Keep:" />
                    <TextBox
                        Width="60"
                        Margin="8,0,0,0"
                        VerticalContentAlignment="Center"
                        Text="5000" />
                    <Button
                        Margin="8,0,0,0"
                        Padding="8,2"
                        Content="Apply" />
                </StackPanel>
            </GroupBox>
        </Grid>
    </DockPanel>
</UserControl> 