{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2A7A42A6-6CA6-489A-AAB6-6433CA7D6FC6}|SurveyPointsManager.csproj|c:\\users\\<USER>\\source\\repos\\surveypointsmanager\\views\\import\\datareviewview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{2A7A42A6-6CA6-489A-AAB6-6433CA7D6FC6}|SurveyPointsManager.csproj|solutionrelative:views\\import\\datareviewview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{2A7A42A6-6CA6-489A-AAB6-6433CA7D6FC6}|SurveyPointsManager.csproj|c:\\users\\<USER>\\source\\repos\\surveypointsmanager\\viewmodels\\importviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A7A42A6-6CA6-489A-AAB6-6433CA7D6FC6}|SurveyPointsManager.csproj|solutionrelative:viewmodels\\importviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A7A42A6-6CA6-489A-AAB6-6433CA7D6FC6}|SurveyPointsManager.csproj|C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\Views\\Import\\DataReviewView.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A7A42A6-6CA6-489A-AAB6-6433CA7D6FC6}|SurveyPointsManager.csproj|solutionrelative:Views\\Import\\DataReviewView.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "ImportViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\ViewModels\\ImportViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\ImportViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\ViewModels\\ImportViewModel.cs", "RelativeToolTip": "ViewModels\\ImportViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T04:30:13.555Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "DataReviewView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\Views\\Import\\DataReviewView.xaml", "RelativeDocumentMoniker": "Views\\Import\\DataReviewView.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\Views\\Import\\DataReviewView.xaml", "RelativeToolTip": "Views\\Import\\DataReviewView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-29T04:15:07.445Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DataReviewView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\Views\\Import\\DataReviewView.xaml.cs", "RelativeDocumentMoniker": "Views\\Import\\DataReviewView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\Views\\Import\\DataReviewView.xaml.cs", "RelativeToolTip": "Views\\Import\\DataReviewView.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T04:09:08.055Z"}]}]}]}