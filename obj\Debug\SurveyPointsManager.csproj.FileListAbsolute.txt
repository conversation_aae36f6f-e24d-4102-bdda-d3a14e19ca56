C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\debug_loader.scr
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\SurveyPointsManager.dll.config
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\SurveyPointsManager.dll
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\SurveyPointsManager.pdb
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\EntityFramework.dll
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\EntityFramework.SqlServer.dll
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\System.Data.SQLite.EF6.dll
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\System.Data.SQLite.Linq.dll
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\EntityFramework.xml
C:\Users\<USER>\source\repos\SurveyPointsManager\bin\Debug\EntityFramework.SqlServer.xml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\SurveyPointsManager.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Resources\Styles\ButtonStyles.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Resources\Styles\IconDictionary.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\cog-off.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\cog.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\content-save-all.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\database-marker.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\export.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\file-export.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\file-import.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\folder-marker-outline.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\help-circle.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\history.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\home.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\import.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\information-box.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\layers.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\magnify.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\map-marker-down.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\map-marker-plus.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\redo.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\restore.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\sync.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\table-cog.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\table-plus.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\trash-can-outline.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\xaml icons\undo.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Export\ExportView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\History\HistoryView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\DataReviewView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\ImportView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\InitialView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\ProcessingView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\StagingView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Main\MainPaletteView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Points\PointsLibraryView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Settings\SettingsView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Shared\SharedHeaderView.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\GeneratedInternalTypeHelper.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\SurveyPointsManager_MarkupCompile.cache
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\SurveyPointsManager_MarkupCompile.lref
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Export\ExportView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\History\HistoryView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\DataReviewView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\ImportView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\InitialView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\ProcessingView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\StagingView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Main\MainPaletteView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Points\PointsLibraryView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Settings\SettingsView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Shared\SharedHeaderView.baml
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\SurveyPointsManager.g.resources
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\SurveyPointsManager.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\SurveyPo.8DFBA5A9.Up2Date
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\SurveyPointsManager.dll
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\SurveyPointsManager.pdb
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\FileConfigurationModal.g.cs
C:\Users\<USER>\source\repos\SurveyPointsManager\obj\Debug\Views\Import\FileConfigurationModal.baml
