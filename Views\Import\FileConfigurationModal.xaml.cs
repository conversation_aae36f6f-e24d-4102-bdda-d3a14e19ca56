using System.Windows;
using SurveyPointsManager.ViewModels;

namespace SurveyPointsManager.Views.Import
{
    /// <summary>
    /// Interaction logic for FileConfigurationModal.xaml
    /// </summary>
    public partial class FileConfigurationModal : Window
    {
        public FileConfigurationModal()
        {
            InitializeComponent();
        }

        public FileConfigurationModal(ImportViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }

        /// <summary>
        /// Gets or sets the dialog result for configuration changes
        /// </summary>
        public bool ConfigurationApplied { get; set; }

        private void OnApplyConfiguration()
        {
            ConfigurationApplied = true;
            DialogResult = true;
            Close();
        }

        private void OnCancelConfiguration()
        {
            ConfigurationApplied = false;
            DialogResult = false;
            Close();
        }

        protected override void OnSourceInitialized(System.EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // Subscribe to view model commands if available
            if (DataContext is ImportViewModel viewModel)
            {
                // Wire up command handlers
                viewModel.ApplyConfigurationCommand = new Infrastructure.Commands.RelayCommand(
                    _ => OnApplyConfiguration(),
                    _ => viewModel.SelectedStagedFile != null);

                viewModel.CancelConfigurationCommand = new Infrastructure.Commands.RelayCommand(
                    _ => OnCancelConfiguration());

                viewModel.ResetConfigurationCommand = new Infrastructure.Commands.RelayCommand(
                    _ => ResetToDefaults(),
                    _ => viewModel.SelectedStagedFile != null);
            }
        }

        private void ResetToDefaults()
        {
            if (DataContext is ImportViewModel viewModel && viewModel.SelectedStagedFile != null)
            {
                // Reset the configuration to defaults
                var defaultConfig = new FileImportConfiguration();
                
                // Copy default values based on file format
                switch (viewModel.SelectedStagedFile.FileFormat)
                {
                    case "CSV":
                        defaultConfig.Delimiter = ",";
                        defaultConfig.HasHeaderRow = true;
                        break;
                    case "TXT":
                        defaultConfig.Delimiter = "\t";
                        defaultConfig.HasHeaderRow = false;
                        break;
                    default:
                        defaultConfig.Delimiter = ",";
                        defaultConfig.HasHeaderRow = true;
                        break;
                }

                // Apply defaults
                viewModel.SelectedStagedFile.ImportConfiguration.Delimiter = defaultConfig.Delimiter;
                viewModel.SelectedStagedFile.ImportConfiguration.HasHeaderRow = defaultConfig.HasHeaderRow;
                viewModel.SelectedStagedFile.ImportConfiguration.SkipRows = 0;
                viewModel.SelectedStagedFile.ImportConfiguration.PointNumberColumn = "Point";
                viewModel.SelectedStagedFile.ImportConfiguration.NorthingColumn = "Northing";
                viewModel.SelectedStagedFile.ImportConfiguration.EastingColumn = "Easting";
                viewModel.SelectedStagedFile.ImportConfiguration.ElevationColumn = "Elevation";
                viewModel.SelectedStagedFile.ImportConfiguration.DescriptionColumn = "Description";
                viewModel.SelectedStagedFile.ImportConfiguration.CoordinateSystem = "Local";
                viewModel.SelectedStagedFile.ImportConfiguration.ScaleFactor = 1.0;
                viewModel.SelectedStagedFile.ImportConfiguration.ValidateCoordinates = true;
                viewModel.SelectedStagedFile.ImportConfiguration.AllowDuplicatePoints = false;

                // Re-detect columns
                viewModel.SelectedStagedFile.DetectColumns();
            }
        }
    }
}
