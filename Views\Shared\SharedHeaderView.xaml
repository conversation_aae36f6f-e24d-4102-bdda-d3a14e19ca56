<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Shared.SharedHeaderView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:SharedHeaderViewModel}"
    d:DesignHeight="60"
    d:DesignWidth="400"
    Background="AliceBlue"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Base style for all header buttons  -->
            <Style x:Key="HeaderButtonStyle" TargetType="Button">
                <Setter Property="Width" Value="36" />
                <Setter Property="Height" Value="32" />
                <Setter Property="Margin" Value="2,0" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Padding" Value="4" />
            </Style>

            <!--  Style for the navigation buttons that includes the active state trigger  -->
            <Style
                x:Key="HeaderNavButtonStyle"
                BasedOn="{StaticResource HeaderButtonStyle}"
                TargetType="Button">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsActive}" Value="True">
                        <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Border
            Background="{DynamicResource SystemColors.ControlBrush}"
            BorderBrush="{DynamicResource SystemColors.ControlDarkBrush}"
            BorderThickness="0,0,0,1">
            <StackPanel
                Margin="4"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Orientation="Horizontal">

                <!--  Logo/Home Button  -->
                <Button
                    Command="{Binding NavigateToMainCommand}"
                    Style="{StaticResource HeaderButtonStyle}"
                    ToolTip="Main">
                    <Path
                        Width="20"
                        Height="20"
                        Data="{StaticResource Icon.Home}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <!--  Separator  -->
                <Separator Margin="4,2" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />

                <!--  Navigation Links  -->
                <Button Command="{Binding NavigateToImportCommand}" ToolTip="Import">
                    <Button.Style>
                        <Style BasedOn="{StaticResource HeaderNavButtonStyle}" TargetType="Button">
                            <Setter Property="DataContext" Value="{Binding}" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsImportTabActive}" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <Path
                        Width="18"
                        Height="18"
                        Data="{StaticResource Icon.Import}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button Command="{Binding NavigateToExportCommand}" ToolTip="Export">
                    <Button.Style>
                        <Style BasedOn="{StaticResource HeaderNavButtonStyle}" TargetType="Button">
                            <Setter Property="DataContext" Value="{Binding}" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsExportTabActive}" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <Path
                        Width="22"
                        Height="22"
                        Data="{StaticResource Icon.Export}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button Command="{Binding NavigateToPointsLibraryCommand}" ToolTip="Points Library">
                    <Button.Style>
                        <Style BasedOn="{StaticResource HeaderNavButtonStyle}" TargetType="Button">
                            <Setter Property="DataContext" Value="{Binding}" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsPointsTabActive}" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <Path
                        Width="18"
                        Height="18"
                        Data="{StaticResource Icon.PointsLibrary}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button Command="{Binding NavigateToHistoryCommand}" ToolTip="History">
                    <Button.Style>
                        <Style BasedOn="{StaticResource HeaderNavButtonStyle}" TargetType="Button">
                            <Setter Property="DataContext" Value="{Binding}" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsHistoryTabActive}" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <Path
                        Width="20"
                        Height="20"
                        Data="{StaticResource Icon.History}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button Command="{Binding NavigateToSettingsCommand}" ToolTip="Settings">
                    <Button.Style>
                        <Style BasedOn="{StaticResource HeaderNavButtonStyle}" TargetType="Button">
                            <Setter Property="DataContext" Value="{Binding}" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsSettingsTabActive}" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <Path
                        Width="18"
                        Height="18"
                        Data="{StaticResource Icon.Settings}"
                        Style="{StaticResource IconStyle}" />
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</UserControl> 