using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SurveyPointsManager.Models;
using SurveyPointsManager.ViewModels;

namespace SurveyPointsManager.Infrastructure.Parsers
{
    /// <summary>
    /// Parser for CSV and text-based survey point files
    /// </summary>
    public class CsvTextParser : IFileParser
    {
        public IEnumerable<string> SupportedFormats => new[] { "CSV", "TXT" };

        public bool CanParse(string filePath, string fileFormat)
        {
            return SupportedFormats.Contains(fileFormat, StringComparer.OrdinalIgnoreCase);
        }

        public async Task<ParseResult> ParseAsync(string filePath, FileImportConfiguration configuration, 
            IProgress<ParseProgress> progress, CancellationToken cancellationToken)
        {
            var result = new ParseResult();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // Read all lines
                var lines = await Task.Run(() => File.ReadAllLines(filePath), cancellationToken);
                var totalLines = lines.Length;

                var parseProgress = new ParseProgress
                {
                    TotalLines = totalLines,
                    CurrentOperation = "Reading file..."
                };
                progress?.Report(parseProgress);

                // Skip header and initial rows if configured
                var startIndex = configuration.SkipRows;
                if (configuration.HasHeaderRow)
                    startIndex++;

                // Process each line
                for (int i = startIndex; i < lines.Length; i++)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var line = lines[i];
                    parseProgress.ProcessedLines = i + 1;
                    parseProgress.CurrentOperation = $"Processing line {i + 1}...";

                    try
                    {
                        var point = ParseLine(line, i + 1, configuration);
                        if (point != null)
                        {
                            result.Points.Add(point);
                            parseProgress.ValidPoints++;
                        }
                    }
                    catch (Exception ex)
                    {
                        var error = new ParseError
                        {
                            LineNumber = i + 1,
                            LineContent = line,
                            ErrorMessage = ex.Message,
                            Severity = ErrorSeverity.Error
                        };
                        result.Errors.Add(error);
                        parseProgress.ErrorCount++;
                    }

                    // Report progress every 100 lines or at the end
                    if (i % 100 == 0 || i == lines.Length - 1)
                    {
                        progress?.Report(parseProgress);
                    }
                }

                result.Success = result.Points.Count > 0;
                result.Statistics = new ParseStatistics
                {
                    TotalLinesProcessed = totalLines,
                    ValidPointsCreated = result.Points.Count,
                    LinesSkipped = startIndex,
                    ErrorCount = result.Errors.Count,
                    WarningCount = result.Warnings.Count,
                    ProcessingTime = stopwatch.Elapsed,
                    SourceFile = Path.GetFileName(filePath)
                };

                parseProgress.CurrentOperation = "Parsing complete";
                progress?.Report(parseProgress);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"Failed to parse file: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
            }

            return result;
        }

        public async Task<ValidationResult> ValidateAsync(string filePath, FileImportConfiguration configuration)
        {
            var result = new ValidationResult();

            try
            {
                if (!File.Exists(filePath))
                {
                    result.Issues.Add("File does not exist");
                    return result;
                }

                var lines = await Task.Run(() => File.ReadAllLines(filePath));
                if (lines.Length == 0)
                {
                    result.Issues.Add("File is empty");
                    return result;
                }

                // Validate file structure
                var sampleLines = lines.Take(Math.Min(10, lines.Length)).ToArray();
                var delimiter = configuration.Delimiter;

                // Check if delimiter is consistent
                var columnCounts = sampleLines.Select(line => line.Split(new[] { delimiter }, StringSplitOptions.None).Length).ToArray();
                if (columnCounts.Distinct().Count() > 1)
                {
                    result.Issues.Add($"Inconsistent number of columns detected. Expected delimiter: '{delimiter}'");
                }

                // Detect columns if header row exists
                if (configuration.HasHeaderRow && lines.Length > 0)
                {
                    var headerLine = lines[configuration.SkipRows];
                    result.DetectedColumns = headerLine.Split(new[] { delimiter }, StringSplitOptions.None)
                                                      .Select(col => col.Trim().Trim('"'))
                                                      .ToList();
                }

                // Estimate point count
                var dataLines = lines.Length - configuration.SkipRows;
                if (configuration.HasHeaderRow)
                    dataLines--;
                
                result.EstimatedPointCount = Math.Max(0, dataLines);
                result.IsValid = result.Issues.Count == 0;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = ex.Message;
                result.Issues.Add($"Validation error: {ex.Message}");
            }

            return result;
        }

        private SurveyPoint ParseLine(string line, int lineNumber, FileImportConfiguration configuration)
        {
            if (string.IsNullOrWhiteSpace(line))
                return null;

            var fields = line.Split(new[] { configuration.Delimiter }, StringSplitOptions.None)
                            .Select(f => f.Trim().Trim('"'))
                            .ToArray();

            // Find column indices
            var pointIndex = FindColumnIndex(configuration.PointNumberColumn, configuration.AvailableColumns);
            var northingIndex = FindColumnIndex(configuration.NorthingColumn, configuration.AvailableColumns);
            var eastingIndex = FindColumnIndex(configuration.EastingColumn, configuration.AvailableColumns);
            var elevationIndex = FindColumnIndex(configuration.ElevationColumn, configuration.AvailableColumns);
            var descriptionIndex = FindColumnIndex(configuration.DescriptionColumn, configuration.AvailableColumns);

            // Extract values
            var pointNumber = GetFieldValue(fields, pointIndex);
            var northingStr = GetFieldValue(fields, northingIndex);
            var eastingStr = GetFieldValue(fields, eastingIndex);
            var elevationStr = GetFieldValue(fields, elevationIndex);
            var description = GetFieldValue(fields, descriptionIndex);

            // Validate required fields
            if (string.IsNullOrEmpty(pointNumber))
                throw new InvalidDataException($"Point number is required at line {lineNumber}");

            if (string.IsNullOrEmpty(northingStr) || string.IsNullOrEmpty(eastingStr))
                throw new InvalidDataException($"Coordinates are required at line {lineNumber}");

            // Parse coordinates
            if (!double.TryParse(northingStr, NumberStyles.Float, CultureInfo.InvariantCulture, out double northing))
                throw new InvalidDataException($"Invalid northing value '{northingStr}' at line {lineNumber}");

            if (!double.TryParse(eastingStr, NumberStyles.Float, CultureInfo.InvariantCulture, out double easting))
                throw new InvalidDataException($"Invalid easting value '{eastingStr}' at line {lineNumber}");

            // Parse elevation (optional)
            double elevation = 0;
            if (!string.IsNullOrEmpty(elevationStr))
            {
                if (!double.TryParse(elevationStr, NumberStyles.Float, CultureInfo.InvariantCulture, out elevation))
                    throw new InvalidDataException($"Invalid elevation value '{elevationStr}' at line {lineNumber}");
            }

            // Apply scale factor if configured
            if (configuration.ScaleFactor != 1.0)
            {
                northing *= configuration.ScaleFactor;
                easting *= configuration.ScaleFactor;
                elevation *= configuration.ScaleFactor;
            }

            // Create survey point
            return new SurveyPoint(pointNumber, northing.ToString("F3"), easting.ToString("F3"), 
                                 elevation.ToString("F3"), description ?? "");
        }

        private int FindColumnIndex(string columnName, IList<string> availableColumns)
        {
            if (string.IsNullOrEmpty(columnName) || availableColumns == null)
                return -1;

            for (int i = 0; i < availableColumns.Count; i++)
            {
                if (string.Equals(availableColumns[i], columnName, StringComparison.OrdinalIgnoreCase))
                    return i;
            }

            return -1;
        }

        private string GetFieldValue(string[] fields, int index)
        {
            if (index < 0 || index >= fields.Length)
                return string.Empty;

            return fields[index];
        }
    }
}
