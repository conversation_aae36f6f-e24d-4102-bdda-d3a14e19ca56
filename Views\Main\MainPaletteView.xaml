<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Main.MainPaletteView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:MainPaletteViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="400"
    Background="AliceBlue"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <Style x:Key="NavButtonStyle" TargetType="Button">
                <Setter Property="Height" Value="32" />
                <Setter Property="MinWidth" Value="60" />
                <Setter Property="Background" Value="{DynamicResource SystemColors.ControlBrushKey}" />
                <Setter Property="Foreground" Value="{DynamicResource SystemColors.ControlTextBrushKey}" />
                <Setter Property="BorderBrush" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="Margin" Value="2,0" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2">
                                <ContentPresenter
                                    Margin="4,2"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="MainLogoButtonStyle" TargetType="Button">
                <Setter Property="Height" Value="80" />
                <Setter Property="Width" Value="80" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderBrush" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                <Setter Property="BorderThickness" Value="2" />
                <Setter Property="Margin" Value="0,0,0,16" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="12">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="FooterLinkStyle" TargetType="Button">
                <Setter Property="Height" Value="24" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="Foreground" Value="{DynamicResource SystemColors.HotTrackBrushKey}" />
                <Setter Property="BorderBrush" Value="Transparent" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Margin" Value="8,0" />
                <Setter Property="FontSize" Value="11" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                                <ContentPresenter
                                    Margin="4,2"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Foreground" Value="{DynamicResource SystemColors.HighlightBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <DockPanel LastChildFill="True">
        <!--  Main Header with Large Logo  -->
        <Border
            Padding="16,20"
            Background="{DynamicResource SystemColors.ControlBrush}"
            BorderBrush="{DynamicResource SystemColors.ControlDarkBrush}"
            BorderThickness="0,0,0,1"
            DockPanel.Dock="Top">

            <StackPanel HorizontalAlignment="Center">
                <!--  Large Logo  -->
                <Button Style="{StaticResource MainLogoButtonStyle}" ToolTip="Survey Points Manager">
                    <Path
                        Width="40"
                        Height="40"
                        Data="{StaticResource Icon.MainLogo}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <!--  Title  -->
                <TextBlock
                    Margin="0,0,0,8"
                    HorizontalAlignment="Center"
                    FontSize="18"
                    FontWeight="Bold"
                    Text="Survey Points Manager" />

                <!--  Subtitle  -->
                <TextBlock
                    HorizontalAlignment="Center"
                    FontSize="12"
                    Foreground="{DynamicResource SystemColors.GrayTextBrushKey}"
                    Text="Professional Survey Point Management" />
            </StackPanel>
        </Border>

        <!--  Footer with Navigation Links  -->
        <Border
            Padding="8,4"
            Background="{DynamicResource SystemColors.ControlLightBrush}"
            BorderBrush="{DynamicResource SystemColors.ControlDarkBrush}"
            BorderThickness="0,1,0,0"
            DockPanel.Dock="Bottom">

            <WrapPanel
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Orientation="Horizontal">

                <Button
                    Command="{Binding NavigateToMainCommand}"
                    Style="{StaticResource FooterLinkStyle}"
                    ToolTip="Main">
                    <Path
                        Width="16"
                        Height="16"
                        Data="{StaticResource Icon.Home}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button
                    Command="{Binding NavigateToImportCommand}"
                    Style="{StaticResource FooterLinkStyle}"
                    ToolTip="Import">
                    <Path
                        Width="16"
                        Height="16"
                        Data="{StaticResource Icon.Import}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button
                    Command="{Binding NavigateToExportCommand}"
                    Style="{StaticResource FooterLinkStyle}"
                    ToolTip="Export">
                    <Path
                        Width="20"
                        Height="20"
                        Data="{StaticResource Icon.Export}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button
                    Command="{Binding NavigateToPointsLibraryCommand}"
                    Style="{StaticResource FooterLinkStyle}"
                    ToolTip="Points Library">
                    <Path
                        Width="16"
                        Height="16"
                        Data="{StaticResource Icon.PointsLibrary}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button
                    Command="{Binding NavigateToHistoryCommand}"
                    Style="{StaticResource FooterLinkStyle}"
                    ToolTip="History">
                    <Path
                        Width="18"
                        Height="18"
                        Data="{StaticResource Icon.History}"
                        Style="{StaticResource IconStyle}" />
                </Button>

                <Button
                    Command="{Binding NavigateToSettingsCommand}"
                    Style="{StaticResource FooterLinkStyle}"
                    ToolTip="Settings">
                    <Path
                        Width="16"
                        Height="16"
                        Data="{StaticResource Icon.Settings}"
                        Style="{StaticResource IconStyle}" />
                </Button>
            </WrapPanel>
        </Border>

        <!--  Main Content Area  -->
        <Grid Margin="16">
            <StackPanel VerticalAlignment="Center">
                <TextBlock
                    Margin="0,0,0,16"
                    HorizontalAlignment="Center"
                    FontSize="16"
                    FontWeight="SemiBold"
                    Text="Welcome to Survey Points Manager" />

                <TextBlock
                    Margin="0,0,0,24"
                    Foreground="{DynamicResource SystemColors.GrayTextBrushKey}"
                    Text="This is your central hub for managing survey points in AutoCAD. Use the navigation links below to access different features."
                    TextAlignment="Center"
                    TextWrapping="Wrap" />
            </StackPanel>
        </Grid>
    </DockPanel>
</UserControl> 