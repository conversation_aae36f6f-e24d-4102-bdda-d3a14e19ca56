<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Import.DataReviewView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ImportViewModel}"
    d:DesignHeight="500"
    d:DesignWidth="400"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter for boolean to visibility -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!-- Style for toolbar buttons -->
            <Style x:Key="ToolbarButtonStyle" TargetType="Button">
                <Setter Property="Width" Value="32" />
                <Setter Property="Height" Value="28" />
                <Setter Property="Margin" Value="2,0" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="BorderBrush" Value="Transparent" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                    <Setter Property="BorderBrush" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Style for error navigator button -->
            <Style x:Key="ErrorNavigatorButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="LightCoral" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Padding" Value="8,4" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                CornerRadius="3">
                                <ContentPresenter
                                    Margin="{TemplateBinding Padding}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="IndianRed" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Style for final action buttons -->
            <Style x:Key="FinalActionButtonStyle" TargetType="Button">
                <Setter Property="Padding" Value="10,6" />
                <Setter Property="Margin" Value="4,0" />
                <Setter Property="Background" Value="{DynamicResource SystemColors.HighlightBrushKey}" />
                <Setter Property="Foreground" Value="{DynamicResource SystemColors.HighlightTextBrushKey}" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                CornerRadius="3">
                                <ContentPresenter
                                    Margin="{TemplateBinding Padding}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlBrushKey}" />
                                    <Setter Property="Foreground" Value="{DynamicResource SystemColors.GrayTextBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Title and Error Navigator -->
        <Grid Grid.Row="0" Margin="16,16,16,8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock
                Grid.Column="0"
                FontSize="18"
                FontWeight="SemiBold"
                Text="Review and Edit Points" />

            <!-- Error Navigator Button -->
            <Button
                Grid.Column="1"
                Command="{Binding NavigateToNextErrorCommand}"
                Style="{StaticResource ErrorNavigatorButtonStyle}"
                ToolTip="Click to navigate to the next error"
                Visibility="{Binding IsErrorNavigatorVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                <TextBlock Text="{Binding ErrorNavigatorText, Mode=OneWay}" />
            </Button>
        </Grid>

        <!-- Toolbar -->
        <Border
            Grid.Row="1"
            Margin="16,0"
            Padding="8,4"
            Background="{DynamicResource SystemColors.ControlBrushKey}"
            BorderBrush="{DynamicResource SystemColors.ControlDarkBrushKey}"
            BorderThickness="1"
            CornerRadius="3">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!-- Undo/Redo buttons -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button
                        Style="{StaticResource ToolbarButtonStyle}"
                        ToolTip="Undo">
                        <Path
                            Width="16"
                            Height="16"
                            Data="{StaticResource Icon.Undo}"
                            Fill="{DynamicResource SystemColors.ControlTextBrushKey}"
                            Stretch="Uniform" />
                    </Button>
                    <Button
                        Style="{StaticResource ToolbarButtonStyle}"
                        ToolTip="Redo">
                        <Path
                            Width="16"
                            Height="16"
                            Data="{StaticResource Icon.Redo}"
                            Fill="{DynamicResource SystemColors.ControlTextBrushKey}"
                            Stretch="Uniform" />
                    </Button>
                </StackPanel>

                <!-- Separator -->
                <Rectangle
                    Grid.Column="1"
                    Width="1"
                    Margin="8,2"
                    Fill="{DynamicResource SystemColors.ControlDarkBrushKey}" />

                <!-- Search box -->
                <TextBox
                    Grid.Column="2"
                    Width="150"
                    Margin="8,0"
                    HorizontalAlignment="Left"
                    VerticalContentAlignment="Center"
                    Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                    ToolTip="Search points by number, coordinates, or description" />

                <!-- Clear search button -->
                <Button
                    Grid.Column="3"
                    Command="{Binding ClearSearchCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="Clear search">
                    <TextBlock
                        FontSize="14"
                        FontWeight="Bold"
                        Text="×" />
                </Button>
            </Grid>
        </Border>

        <!-- Data Grid -->
        <DataGrid
            Grid.Row="2"
            Margin="16,8"
            AutoGenerateColumns="False"
            CanUserAddRows="True"
            CanUserDeleteRows="True"
            EnableRowVirtualization="True"
            ItemsSource="{Binding FilteredSurveyPoints}"
            VirtualizingPanel.IsVirtualizing="True"
            VirtualizingPanel.VirtualizationMode="Recycling">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding PointNumber}"
                    Header="Point #" />
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding Easting}"
                    Header="Easting" />
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding Northing}"
                    Header="Northing" />
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding Elevation}"
                    Header="Elevation" />
                <DataGridTextColumn
                    Width="2*"
                    Binding="{Binding Description}"
                    Header="Description" />
            </DataGrid.Columns>
            
            <!-- Row style for error highlighting -->
            <DataGrid.RowStyle>
                <Style TargetType="DataGridRow">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding HasError}" Value="True">
                            <Setter Property="Background" Value="LightCoral" />
                            <Setter Property="ToolTip" Value="{Binding ErrorMessage}" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </DataGrid.RowStyle>
        </DataGrid>

        <!-- Final Action Buttons -->
        <Border
            Grid.Row="3"
            Margin="16"
            Padding="8"
            Background="{DynamicResource SystemColors.ControlLightBrushKey}"
            BorderBrush="{DynamicResource SystemColors.ControlDarkBrushKey}"
            BorderThickness="1"
            CornerRadius="3">
            <WrapPanel HorizontalAlignment="Center" Orientation="Horizontal">
                <!-- Navigation Actions -->
                <Button
                    Command="{Binding ImportMoreFilesCommand}"
                    Style="{StaticResource FinalActionButtonStyle}"
                    ToolTip="Import additional files while keeping current points">
                    <StackPanel Orientation="Horizontal">
                        <Path
                            Width="16"
                            Height="16"
                            Margin="0,0,5,0"
                            Data="{StaticResource Icon.FileImport}"
                            Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                            Stretch="Uniform" />
                        <TextBlock Text="Import More" />
                    </StackPanel>
                </Button>

                <Button
                    Command="{Binding ReturnToStagingCommand}"
                    Style="{StaticResource FinalActionButtonStyle}"
                    ToolTip="Return to staging to reconfigure files">
                    <StackPanel Orientation="Horizontal">
                        <Path
                            Width="16"
                            Height="16"
                            Margin="0,0,5,0"
                            Data="{StaticResource Icon.Settings}"
                            Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                            Stretch="Uniform" />
                        <TextBlock Text="Reconfigure" />
                    </StackPanel>
                </Button>

                <Button
                    Command="{Binding StartNewImportCommand}"
                    Style="{StaticResource FinalActionButtonStyle}"
                    ToolTip="Start a completely new import session">
                    <StackPanel Orientation="Horizontal">
                        <Path
                            Width="16"
                            Height="16"
                            Margin="0,0,5,0"
                            Data="{StaticResource Icon.Restore}"
                            Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                            Stretch="Uniform" />
                        <TextBlock Text="New Import" />
                    </StackPanel>
                </Button>

                <!-- Separator -->
                <Rectangle
                    Width="1"
                    Height="30"
                    Margin="8,0"
                    VerticalAlignment="Center"
                    Fill="{DynamicResource SystemColors.ControlDarkBrushKey}" />

                <!-- Data Actions -->
                <Button
                    Command="{Binding InsertPointsCommand}"
                    IsEnabled="{Binding CanExecuteFinalActions}"
                    Style="{StaticResource FinalActionButtonStyle}"
                    ToolTip="Insert points into AutoCAD drawing">
                    <StackPanel Orientation="Horizontal">
                        <Path
                            Width="16"
                            Height="16"
                            Margin="0,0,5,0"
                            Data="{StaticResource Icon.MapMarkerDown}"
                            Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                            Stretch="Uniform" />
                        <TextBlock Text="Insert Points" />
                    </StackPanel>
                </Button>

                <Button
                    Command="{Binding ExportPointsCommand}"
                    IsEnabled="{Binding CanExecuteFinalActions}"
                    Style="{StaticResource FinalActionButtonStyle}"
                    ToolTip="Export points to file">
                    <StackPanel Orientation="Horizontal">
                        <Path
                            Width="16"
                            Height="16"
                            Margin="0,0,5,0"
                            Data="{StaticResource Icon.Export}"
                            Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                            Stretch="Uniform" />
                        <TextBlock Text="Export" />
                    </StackPanel>
                </Button>

                <Button
                    Command="{Binding SavePointsCommand}"
                    IsEnabled="{Binding CanExecuteFinalActions}"
                    Style="{StaticResource FinalActionButtonStyle}"
                    ToolTip="Save points to database">
                    <StackPanel Orientation="Horizontal">
                        <Path
                            Width="16"
                            Height="16"
                            Margin="0,0,5,0"
                            Data="{StaticResource Icon.SaveAll}"
                            Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                            Stretch="Uniform" />
                        <TextBlock Text="Save" />
                    </StackPanel>
                </Button>
            </WrapPanel>
        </Border>
    </Grid>
</UserControl>
