<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Import.StagingView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ImportViewModel}"
    d:DesignHeight="500"
    d:DesignWidth="400"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Style for file list items -->
            <Style x:Key="FileListItemStyle" TargetType="ListBoxItem">
                <Setter Property="Padding" Value="8,4" />
                <Setter Property="Margin" Value="0,1" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ListBoxItem">
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="3">
                                <ContentPresenter Margin="{TemplateBinding Padding}" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Style for remove file button -->
            <Style x:Key="RemoveFileButtonStyle" TargetType="Button">
                <Setter Property="Width" Value="20" />
                <Setter Property="Height" Value="20" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Foreground" Value="Red" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                CornerRadius="10">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="LightCoral" />
                                    <Setter Property="Foreground" Value="White" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock
            Grid.Row="0"
            Margin="16,16,16,8"
            FontSize="18"
            FontWeight="SemiBold"
            Text="Configure Import" />

        <!-- Staged Files List -->
        <GroupBox
            Grid.Row="1"
            Margin="16,8"
            Header="Selected Files">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <ListBox
                    ItemContainerStyle="{StaticResource FileListItemStyle}"
                    ItemsSource="{Binding StagedFiles}">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <!-- File icon -->
                                <Path
                                    Grid.Column="0"
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,8,0"
                                    Data="{StaticResource Icon.FileImport}"
                                    Fill="{DynamicResource SystemColors.ControlTextBrushKey}"
                                    Stretch="Uniform" />

                                <!-- File info -->
                                <StackPanel Grid.Column="1" Orientation="Vertical">
                                    <TextBlock
                                        FontWeight="SemiBold"
                                        Text="{Binding FileName}" />
                                    <TextBlock
                                        FontSize="11"
                                        Foreground="{DynamicResource SystemColors.GrayTextBrushKey}">
                                        <Run Text="{Binding FileFormat}" />
                                        <Run Text=" • " />
                                        <Run Text="{Binding FileSizeText}" />
                                    </TextBlock>
                                </StackPanel>

                                <!-- Remove button -->
                                <Button
                                    Grid.Column="3"
                                    Command="{Binding DataContext.RemoveFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    CommandParameter="{Binding}"
                                    Style="{StaticResource RemoveFileButtonStyle}"
                                    ToolTip="Remove this file">
                                    <TextBlock Text="×" />
                                </Button>
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </ScrollViewer>
        </GroupBox>

        <!-- Import Mode Selection -->
        <GroupBox
            Grid.Row="2"
            Margin="16,8"
            Header="Import Mode">
            <StackPanel
                HorizontalAlignment="Center"
                Orientation="Horizontal">
                <RadioButton
                    Margin="0,0,20,0"
                    Content="Replace existing points"
                    IsChecked="{Binding IsReplaceMode}"
                    ToolTip="Clear all existing points before importing" />
                <RadioButton
                    Content="Append to existing points"
                    IsChecked="{Binding IsAppendMode}"
                    ToolTip="Add new points to the existing collection" />
            </StackPanel>
        </GroupBox>

        <!-- Dynamic Settings Panel (placeholder for format-specific settings) -->
        <GroupBox
            Grid.Row="3"
            Margin="16,8"
            Header="Format Settings">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontStyle="Italic"
                Foreground="{DynamicResource SystemColors.GrayTextBrushKey}"
                Text="Format-specific options will appear here..."
                TextAlignment="Center" />
        </GroupBox>

        <!-- Action Buttons -->
        <Grid Grid.Row="4" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- Back Button -->
            <Button
                Grid.Column="0"
                Padding="12,6"
                Command="{Binding GoBackCommand}">
                <StackPanel Orientation="Horizontal">
                    <Path
                        Width="16"
                        Height="16"
                        Margin="0,0,5,0"
                        Data="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"
                        Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                        Stretch="Uniform" />
                    <TextBlock Text="Back" />
                </StackPanel>
            </Button>

            <!-- Load Points Button -->
            <Button
                Grid.Column="2"
                Padding="15,8"
                Background="{DynamicResource SystemColors.HighlightBrushKey}"
                Command="{Binding LoadPointsCommand}"
                Foreground="{DynamicResource SystemColors.HighlightTextBrushKey}"
                IsEnabled="{Binding CanLoadPoints}">
                <StackPanel Orientation="Horizontal">
                    <Path
                        Width="18"
                        Height="18"
                        Margin="0,0,8,0"
                        Data="{StaticResource Icon.Import}"
                        Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                        Stretch="Uniform" />
                    <TextBlock
                        FontSize="14"
                        FontWeight="SemiBold"
                        Text="Load Points into Grid" />
                </StackPanel>
            </Button>
        </Grid>
    </Grid>
</UserControl>
