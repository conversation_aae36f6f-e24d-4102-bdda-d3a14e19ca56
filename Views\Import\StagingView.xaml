<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Import.StagingView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ImportViewModel}"
    d:DesignHeight="500"
    d:DesignWidth="400"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter for boolean to visibility -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!-- Style for file list items -->
            <Style x:Key="FileListItemStyle" TargetType="ListBoxItem">
                <Setter Property="Padding" Value="8,4" />
                <Setter Property="Margin" Value="0,1" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ListBoxItem">
                            <Border
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="3">
                                <ContentPresenter Margin="{TemplateBinding Padding}" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.ControlLightBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Style for remove file button -->
            <Style x:Key="RemoveFileButtonStyle" TargetType="Button">
                <Setter Property="Width" Value="20" />
                <Setter Property="Height" Value="20" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Foreground" Value="Red" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                CornerRadius="10">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="LightCoral" />
                                    <Setter Property="Foreground" Value="White" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Style for configure file button -->
            <Style x:Key="ConfigureFileButtonStyle" TargetType="Button">
                <Setter Property="Width" Value="20" />
                <Setter Property="Height" Value="20" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Foreground" Value="{DynamicResource SystemColors.ControlTextBrushKey}" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                Background="{TemplateBinding Background}"
                                CornerRadius="3">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource SystemColors.HighlightBrushKey}" />
                                    <Setter Property="Foreground" Value="{DynamicResource SystemColors.HighlightTextBrushKey}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock
            Grid.Row="0"
            Margin="16,16,16,8"
            FontSize="18"
            FontWeight="SemiBold"
            Text="Configure Import" />

        <!-- Selected Files Summary -->
        <Border
            Grid.Row="1"
            Margin="16,0,16,8"
            Padding="12,8"
            Background="{DynamicResource SystemColors.ControlLightBrushKey}"
            BorderBrush="{DynamicResource SystemColors.ControlDarkBrushKey}"
            BorderThickness="1"
            CornerRadius="3">
            <StackPanel Orientation="Horizontal">
                <Path
                    Width="16"
                    Height="16"
                    Margin="0,0,8,0"
                    Data="{StaticResource Icon.FileImport}"
                    Fill="{DynamicResource SystemColors.ControlTextBrushKey}"
                    Stretch="Uniform" />
                <TextBlock
                    VerticalAlignment="Center"
                    Text="{Binding StagedFiles.Count, StringFormat=\{0\} files selected}" />
                <TextBlock
                    Margin="16,0,0,0"
                    VerticalAlignment="Center"
                    FontStyle="Italic"
                    Foreground="{DynamicResource SystemColors.GrayTextBrushKey}"
                    Text="Click a file to preview and configure" />
            </StackPanel>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Row="2" Margin="16,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- Files List -->
            <GroupBox
                Grid.Row="0"
                Margin="0,0,0,8"
                Header="Files"
                MaxHeight="180">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <ListBox
                        x:Name="FilesList"
                        ItemContainerStyle="{StaticResource FileListItemStyle}"
                        ItemsSource="{Binding StagedFiles}"
                        SelectedItem="{Binding SelectedStagedFile}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!-- Status indicator -->
                                    <Ellipse
                                        Grid.Column="0"
                                        Width="8"
                                        Height="8"
                                        Margin="0,0,8,0"
                                        VerticalAlignment="Center">
                                        <Ellipse.Style>
                                            <Style TargetType="Ellipse">
                                                <Setter Property="Fill" Value="Green" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsValid}" Value="False">
                                                        <Setter Property="Fill" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Processing">
                                                        <Setter Property="Fill" Value="Orange" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Ellipse.Style>
                                    </Ellipse>

                                    <!-- File info -->
                                    <StackPanel Grid.Column="1" Orientation="Vertical">
                                        <TextBlock
                                            FontWeight="SemiBold"
                                            Text="{Binding FileName, Mode=OneWay}" />
                                        <TextBlock
                                            FontSize="10"
                                            Foreground="{DynamicResource SystemColors.GrayTextBrushKey}">
                                            <Run Text="{Binding FileFormat, Mode=OneWay}" />
                                            <Run Text=" • " />
                                            <Run Text="{Binding FileSizeText, Mode=OneWay}" />
                                        </TextBlock>
                                    </StackPanel>

                                    <!-- Configure button -->
                                    <Button
                                        Grid.Column="2"
                                        Margin="4,0"
                                        Padding="6,2"
                                        Command="{Binding DataContext.ConfigureFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        ToolTip="Configure import settings">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Background" Value="Transparent" />
                                                <Setter Property="BorderThickness" Value="1" />
                                                <Setter Property="BorderBrush" Value="{DynamicResource SystemColors.ControlDarkBrushKey}" />
                                                <Setter Property="Foreground" Value="{DynamicResource SystemColors.ControlTextBrushKey}" />
                                                <Setter Property="FontSize" Value="10" />
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="Button">
                                                            <Border
                                                                Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="2">
                                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                                            </Border>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="{DynamicResource SystemColors.HighlightBrushKey}" />
                                                                    <Setter Property="Foreground" Value="{DynamicResource SystemColors.HighlightTextBrushKey}" />
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </Button.Style>
                                        <TextBlock Text="Config" />
                                    </Button>

                                    <!-- Remove button -->
                                    <Button
                                        Grid.Column="3"
                                        Command="{Binding DataContext.RemoveFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        Style="{StaticResource RemoveFileButtonStyle}"
                                        ToolTip="Remove this file">
                                        <TextBlock Text="×" />
                                    </Button>
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </ScrollViewer>
            </GroupBox>

            <!-- Preview Section -->
            <GroupBox
                Grid.Row="1"
                Header="{Binding SelectedFilePreviewTitle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <ContentControl Content="{Binding SelectedFilePreviewContent}" />
                </ScrollViewer>
            </GroupBox>
        </Grid>

        <!-- Import Mode Selection -->
        <GroupBox
            Grid.Row="3"
            Margin="16,8"
            Header="Import Mode">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <RadioButton
                    Margin="0,0,20,0"
                    Content="Replace existing points"
                    IsChecked="{Binding IsReplaceMode}"
                    ToolTip="Clear all existing points before importing" />
                <RadioButton
                    Content="Append to existing points"
                    IsChecked="{Binding IsAppendMode}"
                    ToolTip="Add new points to the existing collection" />
            </StackPanel>
        </GroupBox>

        <!-- Action Buttons -->
        <Grid Grid.Row="4" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- Back Button -->
            <Button
                Grid.Column="0"
                Padding="12,6"
                Command="{Binding GoBackCommand}">
                <StackPanel Orientation="Horizontal">
                    <Path
                        Width="16"
                        Height="16"
                        Margin="0,0,5,0"
                        Data="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"
                        Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                        Stretch="Uniform" />
                    <TextBlock Text="Back" />
                </StackPanel>
            </Button>

            <!-- Load Points Button -->
            <Button
                Grid.Column="2"
                Padding="15,8"
                Background="{DynamicResource SystemColors.HighlightBrushKey}"
                Command="{Binding LoadPointsCommand}"
                Foreground="{DynamicResource SystemColors.HighlightTextBrushKey}"
                IsEnabled="{Binding CanLoadPoints}">
                <StackPanel Orientation="Horizontal">
                    <Path
                        Width="18"
                        Height="18"
                        Margin="0,0,8,0"
                        Data="{StaticResource Icon.Import}"
                        Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                        Stretch="Uniform" />
                    <TextBlock
                        FontSize="14"
                        FontWeight="SemiBold"
                        Text="Load Points into Grid" />
                </StackPanel>
            </Button>
        </Grid>
    </Grid>
</UserControl>
