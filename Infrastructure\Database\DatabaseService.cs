using System;
using System.Data.SQLite;
using System.IO;

namespace SurveyPointsManager.Infrastructure.Database
{
    public class DatabaseService
    {
        private readonly string _dbPath;

        public DatabaseService()
        {
            // Define a path in the user's documents folder for the database.
            var folder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SurveyPointsManager");
            Directory.CreateDirectory(folder); // Ensure the directory exists.
            _dbPath = Path.Combine(folder, "SurveyPoints.sqlite");
        }

        /// <summary>
        /// Gets the full path to the database file.
        /// </summary>
        public string GetDbPath()
        {
            return _dbPath;
        }

        /// <summary>
        /// Initializes the database by creating the file and the necessary tables if they don't exist.
        /// </summary>
        public void InitializeDatabase()
        {
            if (!File.Exists(_dbPath))
            {
                SQLiteConnection.CreateFile(_dbPath);
            }

            using (var connection = new SQLiteConnection($"Data Source={_dbPath};Version=3;"))
            {
                connection.Open();

                // SQL to create a table for storing point collections, as per the app description.
                string sql = @"
                CREATE TABLE IF NOT EXISTS PointCollections (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    DateSaved TEXT NOT NULL,
                    PointCount INTEGER NOT NULL,
                    Tags TEXT
                );";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Inserts a sample point collection for testing purposes.
        /// </summary>
        public void InsertTestPointCollection()
        {
            using (var connection = new SQLiteConnection($"Data Source={_dbPath};Version=3;"))
            {
                connection.Open();

                string sql = @"
                INSERT INTO PointCollections (Name, DateSaved, PointCount, Tags)
                VALUES (@Name, @DateSaved, @PointCount, @Tags);";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Name", "Test Collection");
                    command.Parameters.AddWithValue("@DateSaved", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.Parameters.AddWithValue("@PointCount", 123);
                    command.Parameters.AddWithValue("@Tags", "test, sample, demo");

                    command.ExecuteNonQuery();
                }
            }
        }
    }
} 