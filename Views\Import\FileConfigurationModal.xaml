<?xml version="1.0" encoding="utf-8" ?>
<Window
    x:Class="SurveyPointsManager.Views.Import.FileConfigurationModal"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    Title="Configure Import Settings"
    Width="450"
    Height="500"
    d:DataContext="{d:DesignInstance Type=vm:ImportViewModel}"
    Background="White"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    WindowStartupLocation="CenterOwner"
    WindowStyle="ToolWindow"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter for boolean to visibility -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        
        <!-- Style for section headers -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Margin" Value="0,16,0,8" />
            <Setter Property="Foreground" Value="{DynamicResource SystemColors.ControlTextBrushKey}" />
        </Style>

        <!-- Style for labels -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Margin" Value="0,4,0,2" />
            <Setter Property="Foreground" Value="{DynamicResource SystemColors.ControlTextBrushKey}" />
        </Style>

            <!-- Style for input controls -->
            <Style x:Key="InputStyle" TargetType="Control">
                <Setter Property="Margin" Value="0,0,0,8" />
                <Setter Property="Height" Value="24" />
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal">
            <Path
                Width="20"
                Height="20"
                Margin="0,0,8,0"
                Data="{StaticResource Icon.Settings}"
                Fill="{DynamicResource SystemColors.ControlTextBrushKey}"
                Stretch="Uniform" />
            <TextBlock
                FontSize="16"
                FontWeight="SemiBold"
                Text="{Binding SelectedStagedFile.FileName, StringFormat='Configure: {0}'}" />
        </StackPanel>

        <!-- Configuration Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- File Format Specific Configuration -->
                <ContentControl Content="{Binding ConfigurationContent}" />
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Grid Grid.Row="2" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- Reset to Defaults Button -->
            <Button
                Grid.Column="0"
                Padding="12,6"
                HorizontalAlignment="Left"
                Command="{Binding ResetConfigurationCommand}"
                Content="Reset to Defaults" />

            <!-- Cancel Button -->
            <Button
                Grid.Column="1"
                Margin="8,0"
                Padding="16,6"
                Command="{Binding CancelConfigurationCommand}"
                Content="Cancel"
                IsCancel="True" />

            <!-- Apply Button -->
            <Button
                Grid.Column="2"
                Padding="16,6"
                Background="{DynamicResource SystemColors.HighlightBrushKey}"
                Command="{Binding ApplyConfigurationCommand}"
                Content="Apply"
                Foreground="{DynamicResource SystemColors.HighlightTextBrushKey}"
                IsDefault="True" />
        </Grid>
    </Grid>
</Window>
