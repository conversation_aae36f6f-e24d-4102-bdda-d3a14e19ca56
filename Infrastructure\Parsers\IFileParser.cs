using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using SurveyPointsManager.Models;
using SurveyPointsManager.ViewModels;

namespace SurveyPointsManager.Infrastructure.Parsers
{
    /// <summary>
    /// Interface for file parsers that can read survey point data from various file formats
    /// </summary>
    public interface IFileParser
    {
        /// <summary>
        /// Gets the file formats supported by this parser
        /// </summary>
        IEnumerable<string> SupportedFormats { get; }

        /// <summary>
        /// Determines if this parser can handle the specified file
        /// </summary>
        bool CanParse(string filePath, string fileFormat);

        /// <summary>
        /// Parses the file and returns survey points
        /// </summary>
        Task<ParseResult> ParseAsync(string filePath, FileImportConfiguration configuration, 
            IProgress<ParseProgress> progress, CancellationToken cancellationToken);

        /// <summary>
        /// Validates the file structure without full parsing
        /// </summary>
        Task<ValidationResult> ValidateAsync(string filePath, FileImportConfiguration configuration);
    }

    /// <summary>
    /// Result of a file parsing operation
    /// </summary>
    public class ParseResult
    {
        public bool Success { get; set; }
        public List<SurveyPoint> Points { get; set; } = new List<SurveyPoint>();
        public List<ParseError> Errors { get; set; } = new List<ParseError>();
        public List<ParseWarning> Warnings { get; set; } = new List<ParseWarning>();
        public ParseStatistics Statistics { get; set; } = new ParseStatistics();
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Result of a file validation operation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
        public string ErrorMessage { get; set; }
        public int EstimatedPointCount { get; set; }
        public List<string> DetectedColumns { get; set; } = new List<string>();
    }

    /// <summary>
    /// Progress information for parsing operations
    /// </summary>
    public class ParseProgress
    {
        public int TotalLines { get; set; }
        public int ProcessedLines { get; set; }
        public int ValidPoints { get; set; }
        public int ErrorCount { get; set; }
        public string CurrentOperation { get; set; }
        public double PercentComplete => TotalLines > 0 ? (double)ProcessedLines / TotalLines * 100 : 0;
    }

    /// <summary>
    /// Parse error information
    /// </summary>
    public class ParseError
    {
        public int LineNumber { get; set; }
        public string LineContent { get; set; }
        public string ErrorMessage { get; set; }
        public string FieldName { get; set; }
        public string FieldValue { get; set; }
        public ErrorSeverity Severity { get; set; }
    }

    /// <summary>
    /// Parse warning information
    /// </summary>
    public class ParseWarning
    {
        public int LineNumber { get; set; }
        public string Message { get; set; }
        public string Suggestion { get; set; }
    }

    /// <summary>
    /// Statistics from parsing operation
    /// </summary>
    public class ParseStatistics
    {
        public int TotalLinesProcessed { get; set; }
        public int ValidPointsCreated { get; set; }
        public int LinesSkipped { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public string SourceFile { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Error severity levels
    /// </summary>
    public enum ErrorSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }
}
