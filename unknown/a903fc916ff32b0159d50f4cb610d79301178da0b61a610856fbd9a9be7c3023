<!--
 *
 * System.Data.SQLite.Core.NetFramework.targets -
 *
 * WARNING: This MSBuild file requires MSBuild 4.0 features.
 *
 * Written by <PERSON> and <PERSON>.
 * Released to the public domain, use at your own risk!
 *
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--
  ******************************************************************************
  **                          Load Per-User Settings                          **
  ******************************************************************************
  -->

  <!--
      NOTE: If the per-user settings file exists, import it now.  The contained
            settings, if any, will override the default ones provided below.
  -->
  <Import Condition="'$(MSBuildThisFileFullPath)' != '' And
                     Exists('$(MSBuildThisFileFullPath).user')"
          Project="$(MSBuildThisFileFullPath).user" />

  <!--
  ******************************************************************************
  **                    SQLite Interop Library Build Items                    **
  ******************************************************************************
  -->

  <ItemGroup>
    <SQLiteInteropFiles Condition="'$(MSBuildThisFileDirectory)' != '' And
                                   HasTrailingSlash('$(MSBuildThisFileDirectory)')"
                        Include="$(MSBuildThisFileDirectory)**\SQLite.Interop.*" />
  </ItemGroup>

  <!--
  ******************************************************************************
  **                   SQLite Interop Library Content Items                   **
  ******************************************************************************
  -->

  <ItemGroup Condition="'$(ContentSQLiteInteropFiles)' != '' And
                        '$(ContentSQLiteInteropFiles)' != 'false' And
                        '@(SQLiteInteropFiles)' != ''">
    <Content Include="@(SQLiteInteropFiles)">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!--
  ******************************************************************************
  **                   SQLite Interop Library Build Targets                   **
  ******************************************************************************
  -->

  <Target Name="CopySQLiteInteropFiles"
          Condition="'$(CopySQLiteInteropFiles)' != 'false' And
                     '$(OutDir)' != '' And
                     HasTrailingSlash('$(OutDir)') And
                     Exists('$(OutDir)')"
          Inputs="@(SQLiteInteropFiles)"
          Outputs="@(SQLiteInteropFiles -> '$(OutDir)%(RecursiveDir)%(Filename)%(Extension)')">
    <!--
        NOTE: Copy "SQLite.Interop.dll" and all related files, for every
              architecture that we support, to the build output directory.
    -->
    <Copy SourceFiles="@(SQLiteInteropFiles)"
          DestinationFiles="@(SQLiteInteropFiles -> '$(OutDir)%(RecursiveDir)%(Filename)%(Extension)')" />
  </Target>

  <!--
  ******************************************************************************
  -->

  <Target Name="CleanSQLiteInteropFiles"
          Condition="'$(CleanSQLiteInteropFiles)' != 'false' And
                     '$(OutDir)' != '' And
                     HasTrailingSlash('$(OutDir)') And
                     Exists('$(OutDir)')">
    <!--
        NOTE: Delete "SQLite.Interop.dll" and all related files, for every
              architecture that we support, from the build output directory.
    -->
    <Delete Files="@(SQLiteInteropFiles -> '$(OutDir)%(RecursiveDir)%(Filename)%(Extension)')" />
  </Target>

  <!--
  ******************************************************************************
  -->

  <Target Name="CollectSQLiteInteropFiles"
          Condition="'$(CollectSQLiteInteropFiles)' != 'false'">
    <ItemGroup>
      <FilesForPackagingFromProject Include="@(SQLiteInteropFiles)">
        <DestinationRelativePath>bin\%(RecursiveDir)%(Filename)%(Extension)</DestinationRelativePath>
      </FilesForPackagingFromProject>
    </ItemGroup>
  </Target>

  <!--
  ******************************************************************************
  **                 SQLite Interop Library Build Properties                  **
  ******************************************************************************
  -->

  <PropertyGroup>
    <PostBuildEventDependsOn>
      $(PostBuildEventDependsOn);
      CopySQLiteInteropFiles;
    </PostBuildEventDependsOn>
    <BuildDependsOn>
      $(BuildDependsOn);
      CopySQLiteInteropFiles;
    </BuildDependsOn>
    <CleanDependsOn>
      $(CleanDependsOn);
      CleanSQLiteInteropFiles;
    </CleanDependsOn>
  </PropertyGroup>

  <!--
  ******************************************************************************
  **     SQLite Interop Library Publish Properties for Visual Studio 20xx     **
  ******************************************************************************
  -->

  <PropertyGroup Condition="'$(VisualStudioVersion)' == '' Or
                            '$(VisualStudioVersion)' == '10.0' Or
                            '$(VisualStudioVersion)' == '11.0' Or
                            '$(VisualStudioVersion)' == '12.0' Or
                            '$(VisualStudioVersion)' == '14.0' Or
                            '$(VisualStudioVersion)' == '15.0' Or
                            '$(VisualStudioVersion)' == '16.0' Or
                            '$(VisualStudioVersion)' == '17.0'">
    <PipelineCollectFilesPhaseDependsOn>
      CollectSQLiteInteropFiles;
      $(PipelineCollectFilesPhaseDependsOn);
    </PipelineCollectFilesPhaseDependsOn>
  </PropertyGroup>
</Project>
