<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Settings.SettingsView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:SurveyPointsManager.Views.Shared"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:SettingsViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="450"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <DockPanel LastChildFill="True">
        <!--  Shared Header  -->
        <shared:SharedHeaderView DockPanel.Dock="Top" />

        <!--  Content  -->
        <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <TextBlock
                    Grid.Row="0"
                    Margin="0,0,0,16"
                    HorizontalAlignment="Center"
                    FontSize="18"
                    FontWeight="SemiBold"
                    Text="Application Settings" />

                <StackPanel Grid.Row="1">
                    <GroupBox Padding="5" Header="Layer Management">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="2,5"
                                VerticalAlignment="Center"
                                Text="Point Layer:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="5,5"
                                VerticalAlignment="Center"
                                Text="SURVEY_POINTS" />
                            <Button
                                Grid.Row="0"
                                Grid.Column="2"
                                Margin="2,5"
                                Padding="8,2"
                                Content="Color" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="2,5"
                                VerticalAlignment="Center"
                                Text="Label Layer:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="5,5"
                                VerticalAlignment="Center"
                                Text="SURVEY_LABELS" />
                            <Button
                                Grid.Row="1"
                                Grid.Column="2"
                                Margin="2,5"
                                Padding="8,2"
                                Content="Color" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="2,5"
                                VerticalAlignment="Center"
                                Text="Table Layer:" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="1"
                                Margin="5,5"
                                VerticalAlignment="Center"
                                Text="SURVEY_TABLES" />
                            <Button
                                Grid.Row="2"
                                Grid.Column="2"
                                Margin="2,5"
                                Padding="8,2"
                                Content="Color" />
                        </Grid>
                    </GroupBox>

                    <GroupBox
                        Margin="0,10,0,0"
                        Padding="5"
                        Header="Point &amp; Label Styles">
                        <StackPanel>
                            <TextBlock Margin="2" Text="Point Symbol:" />
                            <ComboBox Margin="2" SelectedIndex="0">
                                <ComboBoxItem>Circle</ComboBoxItem>
                                <ComboBoxItem>Cross</ComboBoxItem>
                                <ComboBoxItem>X</ComboBoxItem>
                                <ComboBoxItem>Box</ComboBoxItem>
                            </ComboBox>
                            <TextBlock Margin="2,8,2,2" Text="Label Font:" />
                            <TextBox Margin="2" Text="Rubik" />
                        </StackPanel>
                    </GroupBox>

                    <GroupBox
                        Margin="0,10,0,0"
                        Padding="5"
                        Header="General">
                        <StackPanel>
                            <TextBlock Margin="2" Text="UI Theme:" />
                            <ComboBox Margin="2" SelectedIndex="0">
                                <ComboBoxItem>Match AutoCAD</ComboBoxItem>
                                <ComboBoxItem>Light</ComboBoxItem>
                                <ComboBoxItem>Dark</ComboBoxItem>
                            </ComboBox>
                            <Button Margin="2,10,2,2" Content="Clear Recent Export Paths" />
                        </StackPanel>
                    </GroupBox>

                    <GroupBox
                        Margin="0,10,0,0"
                        Padding="5"
                        Header="Development &amp; Testing">
                        <StackPanel>
                            <Button Command="{Binding AddTestDataCommand}" Margin="2,2,2,2">
                                <StackPanel Orientation="Horizontal">
                                    <Path
                                        Width="16"
                                        Height="16"
                                        Margin="0,0,5,0"
                                        Data="{StaticResource Icon.PointsLibrary}"
                                        Style="{StaticResource IconStyle}" />
                                    <TextBlock VerticalAlignment="Center" Text="Add Test Database Entry" />
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>

                <StackPanel
                    Grid.Row="2"
                    Margin="0,20,0,0"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button Padding="10,5">
                        <StackPanel Orientation="Horizontal">
                            <Path
                                Width="16"
                                Height="16"
                                Margin="0,0,5,0"
                                Data="{StaticResource Icon.SaveAll}"
                                Style="{StaticResource IconStyle}" />
                            <TextBlock VerticalAlignment="Center" Text="Save Changes" />
                        </StackPanel>
                    </Button>
                    <Button Margin="10,0,0,0" Padding="10,5">
                        <StackPanel Orientation="Horizontal">
                            <Path
                                Width="16"
                                Height="16"
                                Margin="0,0,5,0"
                                Data="{StaticResource Icon.Restore}"
                                Style="{StaticResource IconStyle}" />
                            <TextBlock VerticalAlignment="Center" Text="Restore Defaults" />
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </ScrollViewer>
    </DockPanel>
</UserControl> 