﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Runtime;
using SurveyPointsManager.Infrastructure.Helpers;
using System;

namespace SurveyPointsManager
{
    /// <summary>
    /// This is the main entry point for the plugin. It handles the application's lifecycle
    /// and defines the user-facing commands.
    /// </summary>
    public class ExtensionApplication : IExtensionApplication
    {
        /// <summary>
        /// This method is called by AutoCAD automatically when the plugin is loaded into memory.
        /// It's the perfect place to set up background services and load data.
        /// </summary>
        public void Initialize()
        {
            // TODO: Check for the user's license entitlement here.
            // Example: LicensingService.VerifyLicense();

            // TODO: Load any persistent settings from a file or the database.
            // Example: SettingsManager.Load();

            // The plugin is now "ready" in the background, waiting for a user command.
            // No UI has been created yet.
        }

        /// <summary>
        /// This method is called by AutoCAD automatically when the plugin is unloaded.
        /// It's the place to dispose of any resources to prevent memory leaks or data corruption.
        /// </summary>
        public void Terminate()
        {
            // TODO: Save any final settings if needed.
            // Example: SettingsManager.Save();
        }

        // --- User Commands ---

        [CommandMethod("SPM")]
        public void ShowSurveyPointsManager()
        {
            // Show the palette set
            PaletteSetHelper.ShowPaletteSet();
        }
    }
}
