{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2A7A42A6-6CA6-489A-AAB6-6433CA7D6FC6}|SurveyPointsManager.csproj|c:\\users\\<USER>\\source\\repos\\surveypointsmanager\\views\\import\\importview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{2A7A42A6-6CA6-489A-AAB6-6433CA7D6FC6}|SurveyPointsManager.csproj|solutionrelative:views\\import\\importview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ImportView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\Views\\Import\\ImportView.xaml", "RelativeDocumentMoniker": "Views\\Import\\ImportView.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\SurveyPointsManager\\Views\\Import\\ImportView.xaml", "RelativeToolTip": "Views\\Import\\ImportView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-28T18:10:58.164Z", "EditorCaption": ""}]}]}]}