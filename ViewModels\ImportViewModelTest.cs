using System;
using System.Linq;
using SurveyPointsManager.ViewModels;
using SurveyPointsManager.Models;

namespace SurveyPointsManager.ViewModels
{
    /// <summary>
    /// Simple test class to verify ImportViewModel functionality
    /// This will be removed once we have proper unit tests
    /// </summary>
    public static class ImportViewModelTest
    {
        /// <summary>
        /// Runs basic tests on the ImportViewModel
        /// </summary>
        public static void RunTests()
        {
            Console.WriteLine("=== ImportViewModel Tests ===");
            
            // Test 1: Initial state
            TestInitialState();
            
            // Test 2: Wizard navigation
            TestWizardNavigation();
            
            // Test 3: Collections and properties
            TestCollectionsAndProperties();
            
            // Test 4: Import mode switching
            TestImportModeSwitch();
            
            // Test 5: Search functionality
            TestSearchFunctionality();
            
            Console.WriteLine("=== All ImportViewModel Tests Completed ===");
        }
        
        private static void TestInitialState()
        {
            Console.WriteLine("\nTest 1: Initial State");
            
            var viewModel = new ImportViewModel();
            
            Console.WriteLine($"Current Step: {viewModel.CurrentStep}");
            Console.WriteLine($"Is Initial View Visible: {viewModel.IsInitialViewVisible}");
            Console.WriteLine($"Total Points: {viewModel.TotalPoints}");
            Console.WriteLine($"Error Count: {viewModel.ErrorCount}");
            Console.WriteLine($"Has Staged Files: {viewModel.HasStagedFiles}");
            Console.WriteLine($"Can Load Points: {viewModel.CanLoadPoints}");
            Console.WriteLine($"Can Execute Final Actions: {viewModel.CanExecuteFinalActions}");
            
            if (viewModel.CurrentStep == ImportViewModel.ImportWizardStep.Initial &&
                viewModel.IsInitialViewVisible &&
                viewModel.TotalPoints == 0 &&
                !viewModel.HasStagedFiles)
            {
                Console.WriteLine("✅ PASS: Initial state is correct");
            }
            else
            {
                Console.WriteLine("❌ FAIL: Initial state test failed");
            }
        }
        
        private static void TestWizardNavigation()
        {
            Console.WriteLine("\nTest 2: Wizard Navigation");
            
            var viewModel = new ImportViewModel();
            
            // Test step transitions
            viewModel.CurrentStep = ImportViewModel.ImportWizardStep.Staging;
            Console.WriteLine($"After setting to Staging - Is Staging Visible: {viewModel.IsStagingViewVisible}");
            Console.WriteLine($"Is Initial Visible: {viewModel.IsInitialViewVisible}");
            
            viewModel.CurrentStep = ImportViewModel.ImportWizardStep.DataReview;
            Console.WriteLine($"After setting to DataReview - Is DataReview Visible: {viewModel.IsDataReviewViewVisible}");
            Console.WriteLine($"Is Staging Visible: {viewModel.IsStagingViewVisible}");
            
            if (viewModel.IsDataReviewViewVisible && !viewModel.IsStagingViewVisible && !viewModel.IsInitialViewVisible)
            {
                Console.WriteLine("✅ PASS: Wizard navigation works correctly");
            }
            else
            {
                Console.WriteLine("❌ FAIL: Wizard navigation test failed");
            }
        }
        
        private static void TestCollectionsAndProperties()
        {
            Console.WriteLine("\nTest 3: Collections and Properties");
            
            var viewModel = new ImportViewModel();
            
            // Test adding survey points
            var point1 = new SurveyPoint("101", "1000", "2000", "150", "Corner");
            var point2 = new SurveyPoint("102", "1100", "2100", "", "Tree");
            var point3 = new SurveyPoint("", "ABC", "2200", "160", "Invalid"); // This should have errors
            
            viewModel.SurveyPoints.Add(point1);
            viewModel.SurveyPoints.Add(point2);
            viewModel.SurveyPoints.Add(point3);
            
            Console.WriteLine($"Total Points: {viewModel.TotalPoints}");
            Console.WriteLine($"Error Count: {viewModel.ErrorCount}");
            Console.WriteLine($"Has Errors: {viewModel.HasErrors}");
            Console.WriteLine($"Status Text: {viewModel.StatusText}");
            Console.WriteLine($"Error Navigator Text: {viewModel.ErrorNavigatorText}");
            Console.WriteLine($"Is Error Navigator Visible: {viewModel.IsErrorNavigatorVisible}");
            
            if (viewModel.TotalPoints == 3 && viewModel.ErrorCount > 0 && viewModel.HasErrors)
            {
                Console.WriteLine("✅ PASS: Collections and error tracking work correctly");
            }
            else
            {
                Console.WriteLine("❌ FAIL: Collections and properties test failed");
            }
        }
        
        private static void TestImportModeSwitch()
        {
            Console.WriteLine("\nTest 4: Import Mode Switching");
            
            var viewModel = new ImportViewModel();
            
            Console.WriteLine($"Initial Import Mode: {viewModel.ImportMode}");
            Console.WriteLine($"Is Replace Mode: {viewModel.IsReplaceMode}");
            Console.WriteLine($"Is Append Mode: {viewModel.IsAppendMode}");
            
            // Switch to append mode
            viewModel.IsAppendMode = true;
            Console.WriteLine($"After setting Append - Import Mode: {viewModel.ImportMode}");
            Console.WriteLine($"Is Append Mode: {viewModel.IsAppendMode}");
            Console.WriteLine($"Is Replace Mode: {viewModel.IsReplaceMode}");
            
            // Switch back to replace mode
            viewModel.IsReplaceMode = true;
            Console.WriteLine($"After setting Replace - Import Mode: {viewModel.ImportMode}");
            Console.WriteLine($"Is Replace Mode: {viewModel.IsReplaceMode}");
            Console.WriteLine($"Is Append Mode: {viewModel.IsAppendMode}");
            
            if (viewModel.ImportMode == ImportViewModel.ImportModeType.Replace &&
                viewModel.IsReplaceMode && !viewModel.IsAppendMode)
            {
                Console.WriteLine("✅ PASS: Import mode switching works correctly");
            }
            else
            {
                Console.WriteLine("❌ FAIL: Import mode switching test failed");
            }
        }
        
        private static void TestSearchFunctionality()
        {
            Console.WriteLine("\nTest 5: Search Functionality");
            
            var viewModel = new ImportViewModel();
            
            // Add some test points
            viewModel.SurveyPoints.Add(new SurveyPoint("A-1", "1000", "2000", "150", "Corner"));
            viewModel.SurveyPoints.Add(new SurveyPoint("B-2", "1100", "2100", "160", "Tree"));
            viewModel.SurveyPoints.Add(new SurveyPoint("A-3", "1200", "2200", "170", "Building"));
            
            Console.WriteLine($"Total Points: {viewModel.SurveyPoints.Count}");
            Console.WriteLine($"Filtered Points (no search): {viewModel.FilteredSurveyPoints.Count}");
            
            // Apply search filter
            viewModel.SearchText = "A-";
            Console.WriteLine($"After searching 'A-': {viewModel.FilteredSurveyPoints.Count} points");
            Console.WriteLine($"Can Clear Search: {viewModel.ClearSearchCommand.CanExecute(null)}");
            
            // Test search for description
            viewModel.SearchText = "Tree";
            Console.WriteLine($"After searching 'Tree': {viewModel.FilteredSurveyPoints.Count} points");
            
            // Clear search
            viewModel.SearchText = "";
            Console.WriteLine($"After clearing search: {viewModel.FilteredSurveyPoints.Count} points");
            
            if (viewModel.FilteredSurveyPoints.Count == viewModel.SurveyPoints.Count)
            {
                Console.WriteLine("✅ PASS: Search functionality works correctly");
            }
            else
            {
                Console.WriteLine("❌ FAIL: Search functionality test failed");
            }
        }
    }
}
