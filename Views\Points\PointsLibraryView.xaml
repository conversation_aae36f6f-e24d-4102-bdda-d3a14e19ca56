<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Points.PointsLibraryView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:SurveyPointsManager.Views.Shared"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:PointsLibraryViewModel}"
    d:DesignHeight="700"
    d:DesignWidth="450"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <DockPanel LastChildFill="True">
        <!--  Shared Header  -->
        <shared:SharedHeaderView DockPanel.Dock="Top" />

        <StatusBar DockPanel.Dock="Bottom">
            <StatusBarItem>
                <TextBlock Text="5 Collections" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="Selected Collection: 'Site A - As-Built' (150 Points)" />
            </StatusBarItem>
        </StatusBar>

        <!--  Content  -->
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  Title  -->
            <TextBlock
                Grid.Row="0"
                Margin="0,0,0,12"
                HorizontalAlignment="Center"
                FontSize="18"
                FontWeight="SemiBold"
                Text="Saved Points Library" />

            <GroupBox Grid.Row="1" Header="Saved Collections">
                <DockPanel>
                    <WrapPanel Margin="0,0,0,5" DockPanel.Dock="Top">
                        <Button Padding="5,2" ToolTip="Save current session points as a new collection">
                            <StackPanel Orientation="Horizontal">
                                <Path
                                    Width="14"
                                    Height="14"
                                    Margin="0,0,4,0"
                                    Data="{StaticResource Icon.SaveAll}"
                                    Style="{StaticResource IconStyle}" />
                                <TextBlock Text="Save Current" />
                            </StackPanel>
                        </Button>
                        <Button
                            Margin="5,0,0,0"
                            Padding="5,2"
                            ToolTip="Rename selected collection">
                            <TextBlock Text="Rename" />
                        </Button>
                        <Button
                            Margin="5,0,0,0"
                            Padding="5,2"
                            ToolTip="Delete selected collection">
                            <TextBlock Text="Delete" />
                        </Button>
                        <Button
                            Margin="5,0,0,0"
                            Padding="5,2"
                            ToolTip="Merge selected collections">
                            <TextBlock Text="Merge" />
                        </Button>
                        <Button
                            Margin="15,0,0,0"
                            Padding="5,2"
                            ToolTip="Test Database Connection"
                            Command="{Binding TestDatabaseCommand}">
                            <StackPanel Orientation="Horizontal">
                                <Path
                                    Width="14"
                                    Height="14"
                                    Margin="0,0,4,0"
                                    Data="{StaticResource Icon.Home}"
                                    Style="{StaticResource IconStyle}" />
                                <TextBlock Text="Test Database" />
                            </StackPanel>
                        </Button>
                    </WrapPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBox VerticalContentAlignment="Center" Text="Search by name, date, or tag..." />
                        <Path
                            Grid.Column="1"
                            Width="16"
                            Height="16"
                            Margin="5,0"
                            VerticalAlignment="Center"
                            Data="{StaticResource Icon.Search}"
                            Fill="Gray"
                            Style="{StaticResource IconStyle}" />
                    </Grid>
                </DockPanel>
            </GroupBox>

            <DataGrid
                Grid.Row="2"
                Margin="0,5,0,0"
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn
                        Width="2*"
                        Binding="{Binding Name}"
                        Header="Collection Name" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Date}"
                        Header="Date Saved" />
                    <DataGridTextColumn
                        Width="Auto"
                        Binding="{Binding PointCount}"
                        Header="Points" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Tags}"
                        Header="Tags" />
                </DataGrid.Columns>
            </DataGrid>

            <GroupBox
                Grid.Row="3"
                Margin="0,10,0,0"
                Header="Points in Selected Collection">
                <WrapPanel>
                    <Button Padding="5,2" ToolTip="Export this collection to a file">
                        <StackPanel Orientation="Horizontal">
                            <Path
                                Width="14"
                                Height="14"
                                Margin="0,0,4,0"
                                Data="{StaticResource Icon.FileExport}"
                                Style="{StaticResource IconStyle}" />
                            <TextBlock Text="Export" />
                        </StackPanel>
                    </Button>
                    <Button
                        Margin="5,0,0,0"
                        Padding="5,2"
                        ToolTip="Insert these points into the current drawing">
                        <StackPanel Orientation="Horizontal">
                            <Path
                                Width="14"
                                Height="14"
                                Margin="0,0,4,0"
                                Data="{StaticResource Icon.PointsLibrary}"
                                Style="{StaticResource IconStyle}" />
                            <TextBlock Text="Insert to Drawing" />
                        </StackPanel>
                    </Button>
                </WrapPanel>
            </GroupBox>

            <DataGrid
                Grid.Row="4"
                Margin="0,5,0,0"
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                CanUserDeleteRows="False"
                IsReadOnly="True">
                <DataGrid.Columns>
                    <DataGridTextColumn
                        Width="60"
                        Binding="{Binding PointNumber}"
                        Header="Point #" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Northing}"
                        Header="Northing" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Easting}"
                        Header="Easting" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Elevation}"
                        Header="Elevation" />
                    <DataGridTextColumn
                        Width="2*"
                        Binding="{Binding Description}"
                        Header="Description" />
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </DockPanel>
</UserControl> 