using System;
using SurveyPointsManager.Models;

namespace SurveyPointsManager.Tests
{
    /// <summary>
    /// Test to verify SurveyPoint model functionality
    /// </summary>
    class TestSurveyPoint
    {
        static void Main()
        {
            Console.WriteLine("=== SURVEY POINT MODEL TEST ===");
            Console.WriteLine("Testing SurveyPoint model creation, validation, and properties");
            Console.WriteLine();
            
            try
            {
                // Test 1: Valid SurveyPoint creation
                Console.WriteLine("🔍 Testing Valid SurveyPoint Creation...");
                var validPoint = new SurveyPoint("101", "1000.50", "2000.75", "150.25", "Corner monument");
                
                Console.WriteLine($"  Point Number: {validPoint.PointNumber}");
                Console.WriteLine($"  Northing: {validPoint.Northing}");
                Console.WriteLine($"  Easting: {validPoint.Easting}");
                Console.WriteLine($"  Elevation: {validPoint.Elevation}");
                Console.WriteLine($"  Description: {validPoint.Description}");
                Console.WriteLine($"  Has Error: {validPoint.HasError}");
                Console.WriteLine($"  Error Message: '{validPoint.ErrorMessage}'");
                Console.WriteLine($"  ToString: {validPoint}");
                
                if (!validPoint.HasError && validPoint.PointNumber == "101")
                {
                    Console.WriteLine("  ✅ Valid SurveyPoint creation successful");
                }
                else
                {
                    Console.WriteLine("  ❌ Valid SurveyPoint creation failed");
                }
                
                // Test 2: Invalid SurveyPoint (empty point number)
                Console.WriteLine("\n🔍 Testing Invalid SurveyPoint (Empty Point Number)...");
                var invalidPoint1 = new SurveyPoint("", "1000", "2000", "150", "Test");
                
                Console.WriteLine($"  Has Error: {invalidPoint1.HasError}");
                Console.WriteLine($"  Error Message: '{invalidPoint1.ErrorMessage}'");
                
                if (invalidPoint1.HasError && !string.IsNullOrEmpty(invalidPoint1.ErrorMessage))
                {
                    Console.WriteLine("  ✅ Empty point number validation working");
                }
                else
                {
                    Console.WriteLine("  ❌ Empty point number validation failed");
                }
                
                // Test 3: Invalid coordinates
                Console.WriteLine("\n🔍 Testing Invalid Coordinates...");
                var invalidPoint2 = new SurveyPoint("102", "invalid", "2000", "150", "Test");
                
                Console.WriteLine($"  Has Error: {invalidPoint2.HasError}");
                Console.WriteLine($"  Error Message: '{invalidPoint2.ErrorMessage}'");
                
                if (invalidPoint2.HasError)
                {
                    Console.WriteLine("  ✅ Invalid coordinate validation working");
                }
                else
                {
                    Console.WriteLine("  ❌ Invalid coordinate validation failed");
                }
                
                // Test 4: Property change notifications
                Console.WriteLine("\n🔍 Testing Property Change Notifications...");
                bool propertyChanged = false;
                validPoint.PropertyChanged += (s, e) => {
                    propertyChanged = true;
                    Console.WriteLine($"  Property changed: {e.PropertyName}");
                };
                
                validPoint.Description = "Updated description";
                
                if (propertyChanged)
                {
                    Console.WriteLine("  ✅ Property change notifications working");
                }
                else
                {
                    Console.WriteLine("  ❌ Property change notifications failed");
                }
                
                Console.WriteLine("\n🎉 SURVEY POINT MODEL TEST COMPLETE! 🎉");
                Console.WriteLine("✅ SurveyPoint model is working correctly");
                Console.WriteLine("✅ Validation logic is functional");
                Console.WriteLine("✅ Property change notifications are working");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
