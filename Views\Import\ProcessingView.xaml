<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Import.ProcessingView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ImportViewModel}"
    d:DesignHeight="500"
    d:DesignWidth="400"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Style for the processing animation -->
            <Style x:Key="ProcessingIconStyle" TargetType="Path">
                <Setter Property="Fill" Value="{DynamicResource SystemColors.HighlightBrushKey}" />
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5" />
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <RotateTransform />
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsProcessing}" Value="True">
                        <DataTrigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard RepeatBehavior="Forever">
                                    <DoubleAnimation
                                        Storyboard.TargetProperty="RenderTransform.Angle"
                                        From="0"
                                        To="360"
                                        Duration="0:0:2" />
                                </Storyboard>
                            </BeginStoryboard>
                        </DataTrigger.EnterActions>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <!-- Main content area -->
        <StackPanel
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Orientation="Vertical">

            <!-- Processing icon with animation -->
            <Border
                Width="80"
                Height="80"
                Margin="0,0,0,30"
                Background="{DynamicResource SystemColors.ControlLightBrushKey}"
                BorderBrush="{DynamicResource SystemColors.HighlightBrushKey}"
                BorderThickness="2"
                CornerRadius="40">
                <Path
                    Width="40"
                    Height="40"
                    Data="{StaticResource Icon.Sync}"
                    Style="{StaticResource ProcessingIconStyle}"
                    Stretch="Uniform" />
            </Border>

            <!-- Processing title -->
            <TextBlock
                Margin="0,0,0,10"
                FontSize="18"
                FontWeight="SemiBold"
                Text="Processing Files"
                TextAlignment="Center" />

            <!-- Current status message -->
            <TextBlock
                Margin="0,0,0,20"
                FontSize="14"
                Foreground="{DynamicResource SystemColors.GrayTextBrushKey}"
                Text="{Binding ProcessingMessage}"
                TextAlignment="Center" />

            <!-- Progress bar -->
            <ProgressBar
                Width="300"
                Height="20"
                Margin="0,0,0,15"
                Background="{DynamicResource SystemColors.ControlLightBrushKey}"
                BorderBrush="{DynamicResource SystemColors.ControlDarkBrushKey}"
                BorderThickness="1"
                Foreground="{DynamicResource SystemColors.HighlightBrushKey}"
                Value="{Binding ProcessingProgress}" />

            <!-- Progress percentage -->
            <TextBlock
                FontSize="12"
                Foreground="{DynamicResource SystemColors.GrayTextBrushKey}"
                TextAlignment="Center">
                <Run Text="{Binding ProcessingProgress, StringFormat=F0}" />
                <Run Text="% Complete" />
            </TextBlock>

            <!-- Processing details -->
            <Border
                Margin="0,30,0,0"
                Padding="15,10"
                Background="{DynamicResource SystemColors.ControlBrushKey}"
                BorderBrush="{DynamicResource SystemColors.ControlDarkBrushKey}"
                BorderThickness="1"
                CornerRadius="5">
                <StackPanel Orientation="Vertical">
                    <TextBlock
                        FontSize="12"
                        FontWeight="SemiBold"
                        Text="Processing Steps:" />
                    <TextBlock
                        Margin="0,5,0,0"
                        FontSize="11"
                        Foreground="{DynamicResource SystemColors.GrayTextBrushKey}">
                        <Run Text="• Reading file contents" />
                        <LineBreak />
                        <Run Text="• Parsing coordinate data" />
                        <LineBreak />
                        <Run Text="• Validating point information" />
                        <LineBreak />
                        <Run Text="• Loading into data grid" />
                    </TextBlock>
                </StackPanel>
            </Border>

            <!-- Note about responsiveness -->
            <TextBlock
                Margin="0,20,0,0"
                FontSize="11"
                FontStyle="Italic"
                Foreground="{DynamicResource SystemColors.GrayTextBrushKey}"
                Text="AutoCAD remains responsive during processing"
                TextAlignment="Center" />
        </StackPanel>

        <!-- Cancel button (for future implementation) -->
        <Button
            Width="80"
            Height="30"
            Margin="16"
            HorizontalAlignment="Right"
            VerticalAlignment="Bottom"
            Background="LightCoral"
            Content="Cancel"
            Foreground="White"
            IsEnabled="False"
            ToolTip="Cancel processing (coming soon)"
            Visibility="Collapsed" />
    </Grid>
</UserControl>
