using Autodesk.AutoCAD.Windows;
using SurveyPointsManager.Views.Main;
using SurveyPointsManager.Views.Import;
using SurveyPointsManager.Views.Export;
using SurveyPointsManager.Views.Points;
using SurveyPointsManager.Views.Settings;
using SurveyPointsManager.Views.History;
using System;

namespace SurveyPointsManager.Infrastructure.Helpers
{
    public static class PaletteSetHelper
    {
        private static PaletteSet _paletteSet;
        private static int _activeIndex = -1;
        
        // Tab indices for navigation
        public const int TAB_MAIN = 0;
        public const int TAB_IMPORT = 1;
        public const int TAB_EXPORT = 2;
        public const int TAB_POINTS = 3;
        public const int TAB_HISTORY = 4;
        public const int TAB_SETTINGS = 5;

        // Event for active tab changes
        public static event EventHandler<int> ActiveTabChanged;

        public static void ShowPaletteSet()
        {
            if (_paletteSet == null)
            {
                // Create new PaletteSet
                _paletteSet = new PaletteSet("Survey Points Manager")
                {
                    Style = PaletteSetStyles.ShowPropertiesMenu |
                           PaletteSetStyles.ShowAutoHideButton |
                           PaletteSetStyles.ShowCloseButton,
                    MinimumSize = new System.Drawing.Size(400, 600),
                    Size = new System.Drawing.Size(400, 600),
                    DockEnabled = (DockSides)((int)DockSides.Left + (int)DockSides.Right) // Allow docking to left and right
                };

                // Only create the main tab initially for fast startup
                AddMainTabOnly();

                // Set initial dock state
                _paletteSet.Dock = DockSides.Right;

                // Set initial active tab
                _activeIndex = TAB_MAIN;
            }

            // Show the palette set
            _paletteSet.Visible = true;
        }

        private static void AddMainTabOnly()
        {
            // Only add Main tab initially for fast startup
            var mainView = new MainPaletteView();
            _paletteSet.AddVisual("Main", mainView);
        }

        private static void EnsureTabExists(int tabIndex, string tabName, System.Func<System.Windows.Controls.UserControl> createView)
        {
            // Check if we need to create more tabs
            while (_paletteSet.Count <= tabIndex)
            {
                System.Windows.Controls.UserControl view = null;
                string name = "";

                switch (_paletteSet.Count)
                {
                    case TAB_IMPORT:
                        view = new ImportView();
                        name = "Import";
                        break;
                    case TAB_EXPORT:
                        view = new ExportView();
                        name = "Export";
                        break;
                    case TAB_POINTS:
                        view = new PointsLibraryView();
                        name = "Points Library";
                        break;
                    case TAB_HISTORY:
                        view = new HistoryView();
                        name = "History";
                        break;
                    case TAB_SETTINGS:
                        view = new SettingsView();
                        name = "Settings";
                        break;
                    default:
                        return; // Unknown tab index
                }

                if (view != null)
                {
                    _paletteSet.AddVisual(name, view);
                }
            }
        }

        public static void ActivateTab(int tabIndex)
        {
            if (_paletteSet != null && tabIndex >= 0)
            {
                // Ensure the tab exists before activating
                EnsureTabExists(tabIndex, "", null);

                if (tabIndex < _paletteSet.Count)
                {
                    _paletteSet.Activate(tabIndex);
                    _activeIndex = tabIndex;
                    ActiveTabChanged?.Invoke(null, tabIndex);
                }
            }
        }

        public static int GetActiveTabIndex()
        {
            return _activeIndex;
        }

        public static void ActivateMainTab()
        {
            ActivateTab(TAB_MAIN);
        }

        public static void ActivateImportTab()
        {
            ActivateTab(TAB_IMPORT);
        }

        public static void ActivateExportTab()
        {
            ActivateTab(TAB_EXPORT);
        }

        public static void ActivatePointsTab()
        {
            ActivateTab(TAB_POINTS);
        }

        public static void ActivateHistoryTab()
        {
            ActivateTab(TAB_HISTORY);
        }

        public static void ActivateSettingsTab()
        {
            ActivateTab(TAB_SETTINGS);
        }
    }
} 