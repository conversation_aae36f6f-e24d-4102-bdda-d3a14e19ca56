<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="SurveyPointsManager.Views.Export.ExportView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:SurveyPointsManager.Views.Shared"
    xmlns:vm="clr-namespace:SurveyPointsManager.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ExportViewModel}"
    d:DesignHeight="700"
    d:DesignWidth="450"
    Background="White"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../../Resources/Styles/IconDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <DockPanel LastChildFill="True">
        <!--  Shared Header  -->
        <shared:SharedHeaderView DockPanel.Dock="Top" />

        <StatusBar DockPanel.Dock="Bottom">
            <StatusBarItem>
                <TextBlock Text="52 Points Picked" />
            </StatusBarItem>
        </StatusBar>

        <!--  Content  -->
        <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <TextBlock
                    Grid.Row="0"
                    Margin="0,0,0,16"
                    HorizontalAlignment="Center"
                    FontSize="18"
                    FontWeight="SemiBold"
                    Text="Export from Drawing" />

                <GroupBox
                    Grid.Row="1"
                    Padding="5"
                    Header="Point Picking Options">
                    <StackPanel>
                        <TextBlock Margin="2" Text="Pick Mode:" />
                        <StackPanel Margin="2" Orientation="Horizontal">
                            <RadioButton Content="Point-by-Point" IsChecked="True" />
                            <RadioButton Margin="10,0,0,0" Content="Bulk Pick" />
                            <RadioButton Margin="10,0,0,0" Content="From Objects" />
                        </StackPanel>
                        <TextBlock Margin="2,8,2,2" Text="Default Description:" />
                        <TextBox Margin="2" />
                    </StackPanel>
                </GroupBox>

                <GroupBox
                    Grid.Row="2"
                    Margin="0,10,0,0"
                    Header="Picked Points">
                    <DataGrid
                        AutoGenerateColumns="False"
                        CanUserAddRows="True"
                        CanUserDeleteRows="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding PointNumber}"
                                Header="Point #" />
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding Northing}"
                                Header="Northing" />
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding Easting}"
                                Header="Easting" />
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding Elevation}"
                                Header="Elevation" />
                            <DataGridTextColumn
                                Width="2*"
                                Binding="{Binding Description}"
                                Header="Description" />
                        </DataGrid.Columns>
                    </DataGrid>
                </GroupBox>

                <GroupBox
                    Grid.Row="3"
                    Margin="0,10,0,0"
                    Padding="5"
                    Header="Export Format">
                    <StackPanel>
                        <TextBlock Margin="2" Text="File Format:" />
                        <ComboBox Margin="2" SelectedIndex="0">
                            <ComboBoxItem>CSV / TXT</ComboBoxItem>
                            <ComboBoxItem>LandXML (*.xml)</ComboBoxItem>
                            <ComboBoxItem>DXF (*.dxf)</ComboBoxItem>
                            <ComboBoxItem>DWG (*.dwg)</ComboBoxItem>
                            <ComboBoxItem>Shapefile (*.shp)</ComboBoxItem>
                            <ComboBoxItem>KML / KMZ</ComboBoxItem>
                            <ComboBoxItem>GPX (*.gpx)</ComboBoxItem>
                            <ComboBoxItem>GeoJSON (*.geojson)</ComboBoxItem>
                            <ComboBoxItem>SDR (*.sdr)</ComboBoxItem>
                            <ComboBoxItem>IDX (*.idx)</ComboBoxItem>
                            <ComboBoxItem>GSI (*.gsi)</ComboBoxItem>
                        </ComboBox>
                        <TextBlock Margin="2,10,2,2" Text="Save Location:" />
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBox
                                Grid.Column="0"
                                Margin="0,0,8,0"
                                VerticalContentAlignment="Center"
                                Text="C:\Users\<USER>\Documents" />
                            <Button
                                Grid.Column="1"
                                Padding="10,5"
                                ToolTip="Browse for save location">
                                <Path
                                    Width="16"
                                    Height="16"
                                    Data="{StaticResource Icon.MainLogo}"
                                    Style="{StaticResource IconStyle}" />
                            </Button>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <Button
                    Grid.Row="4"
                    Margin="0,20,0,0"
                    Padding="10,8"
                    HorizontalAlignment="Center">
                    <StackPanel Orientation="Horizontal">
                        <Path
                            Width="20"
                            Height="20"
                            Margin="0,0,8,0"
                            Data="{StaticResource Icon.FileExport}"
                            Style="{StaticResource IconStyle}" />
                        <TextBlock
                            VerticalAlignment="Center"
                            FontSize="14"
                            Text="Export to File" />
                    </StackPanel>
                </Button>
            </Grid>
        </ScrollViewer>
    </DockPanel>
</UserControl> 