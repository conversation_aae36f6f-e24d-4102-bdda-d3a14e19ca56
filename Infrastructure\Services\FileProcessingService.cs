using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SurveyPointsManager.Infrastructure.Parsers;
using SurveyPointsManager.Models;
using SurveyPointsManager.ViewModels;
using static SurveyPointsManager.ViewModels.ImportViewModel;

namespace SurveyPointsManager.Infrastructure.Services
{
    /// <summary>
    /// Service for processing multiple files in the background with progress reporting
    /// </summary>
    public class FileProcessingService
    {
        private readonly FileParserFactory _parserFactory;
        private CancellationTokenSource _cancellationTokenSource;

        public FileProcessingService()
        {
            _parserFactory = new FileParserFactory();
        }

        /// <summary>
        /// Event fired when processing progress changes
        /// </summary>
        public event EventHandler<ProcessingProgressEventArgs> ProgressChanged;

        /// <summary>
        /// Event fired when processing completes
        /// </summary>
        public event EventHandler<ProcessingCompletedEventArgs> ProcessingCompleted;

        /// <summary>
        /// Gets whether processing is currently running
        /// </summary>
        public bool IsProcessing { get; private set; }

        /// <summary>
        /// Processes multiple files asynchronously
        /// </summary>
        public async Task ProcessFilesAsync(IEnumerable<StagedFile> stagedFiles, ImportModeType importMode)
        {
            if (IsProcessing)
                throw new InvalidOperationException("Processing is already in progress");

            IsProcessing = true;
            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                var files = stagedFiles.ToList();
                var allPoints = new List<SurveyPoint>();
                var allErrors = new List<FileProcessingError>();
                var processingStats = new ProcessingStatistics();

                var overallProgress = new ProcessingProgress
                {
                    TotalFiles = files.Count,
                    CurrentOperation = "Starting processing..."
                };

                ReportProgress(overallProgress);

                for (int i = 0; i < files.Count; i++)
                {
                    var file = files[i];
                    overallProgress.CurrentFile = file.FileName;
                    overallProgress.ProcessedFiles = i;
                    overallProgress.CurrentOperation = $"Processing {file.FileName}...";
                    ReportProgress(overallProgress);

                    try
                    {
                        var fileResult = await ProcessSingleFileAsync(file, overallProgress);
                        
                        if (fileResult.Success)
                        {
                            allPoints.AddRange(fileResult.Points);
                            processingStats.SuccessfulFiles++;
                        }
                        else
                        {
                            processingStats.FailedFiles++;
                            allErrors.Add(new FileProcessingError
                            {
                                FileName = file.FileName,
                                ErrorMessage = fileResult.ErrorMessage,
                                Errors = fileResult.Errors
                            });
                        }

                        processingStats.TotalPointsProcessed += fileResult.Points.Count;
                        processingStats.TotalErrorsFound += fileResult.Errors.Count;
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        processingStats.FailedFiles++;
                        allErrors.Add(new FileProcessingError
                        {
                            FileName = file.FileName,
                            ErrorMessage = ex.Message
                        });
                    }
                }

                overallProgress.ProcessedFiles = files.Count;
                overallProgress.CurrentOperation = "Processing complete";
                overallProgress.IsComplete = true;
                ReportProgress(overallProgress);

                // Fire completion event
                var completedArgs = new ProcessingCompletedEventArgs
                {
                    Success = !_cancellationTokenSource.Token.IsCancellationRequested,
                    Points = allPoints,
                    Errors = allErrors,
                    Statistics = processingStats,
                    ImportMode = importMode
                };

                ProcessingCompleted?.Invoke(this, completedArgs);
            }
            finally
            {
                IsProcessing = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// Cancels the current processing operation
        /// </summary>
        public void CancelProcessing()
        {
            _cancellationTokenSource?.Cancel();
        }

        private async Task<ParseResult> ProcessSingleFileAsync(StagedFile stagedFile, ProcessingProgress overallProgress)
        {
            var parser = _parserFactory.GetParser(stagedFile.FilePath, stagedFile.FileFormat);
            if (parser == null)
            {
                return new ParseResult
                {
                    Success = false,
                    ErrorMessage = $"No parser available for format: {stagedFile.FileFormat}"
                };
            }

            var fileProgress = new Progress<ParseProgress>(progress =>
            {
                overallProgress.CurrentFileProgress = progress.PercentComplete;
                overallProgress.CurrentOperation = $"{stagedFile.FileName}: {progress.CurrentOperation}";
                ReportProgress(overallProgress);
            });

            return await parser.ParseAsync(stagedFile.FilePath, stagedFile.ImportConfiguration, 
                                         fileProgress, _cancellationTokenSource.Token);
        }

        private void ReportProgress(ProcessingProgress progress)
        {
            ProgressChanged?.Invoke(this, new ProcessingProgressEventArgs { Progress = progress });
        }
    }

    /// <summary>
    /// Progress information for file processing
    /// </summary>
    public class ProcessingProgress
    {
        public int TotalFiles { get; set; }
        public int ProcessedFiles { get; set; }
        public string CurrentFile { get; set; }
        public double CurrentFileProgress { get; set; }
        public string CurrentOperation { get; set; }
        public bool IsComplete { get; set; }
        public double OverallProgress => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;
    }

    /// <summary>
    /// Statistics from processing operation
    /// </summary>
    public class ProcessingStatistics
    {
        public int SuccessfulFiles { get; set; }
        public int FailedFiles { get; set; }
        public int TotalPointsProcessed { get; set; }
        public int TotalErrorsFound { get; set; }
        public DateTime StartTime { get; set; } = DateTime.Now;
        public DateTime EndTime { get; set; }
        public TimeSpan ProcessingTime => EndTime - StartTime;
    }

    /// <summary>
    /// Error information for a specific file
    /// </summary>
    public class FileProcessingError
    {
        public string FileName { get; set; }
        public string ErrorMessage { get; set; }
        public List<ParseError> Errors { get; set; } = new List<ParseError>();
    }

    /// <summary>
    /// Event arguments for processing progress
    /// </summary>
    public class ProcessingProgressEventArgs : EventArgs
    {
        public ProcessingProgress Progress { get; set; }
    }

    /// <summary>
    /// Event arguments for processing completion
    /// </summary>
    public class ProcessingCompletedEventArgs : EventArgs
    {
        public bool Success { get; set; }
        public List<SurveyPoint> Points { get; set; }
        public List<FileProcessingError> Errors { get; set; }
        public ProcessingStatistics Statistics { get; set; }
        public ImportModeType ImportMode { get; set; }
    }
}
