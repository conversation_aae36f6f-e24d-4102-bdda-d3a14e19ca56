using SurveyPointsManager.Infrastructure.Base;
using SurveyPointsManager.Infrastructure.Commands;
using SurveyPointsManager.Models;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Windows.Input;
using Microsoft.Win32;

namespace SurveyPointsManager.ViewModels
{
    /// <summary>
    /// ViewModel for the Import module with wizard-style navigation and comprehensive state management
    /// </summary>
    public class ImportViewModel : ViewModelBase
    {
        #region Private Fields
        private string _title = "Import";
        private ImportWizardStep _currentStep;
        private bool _isProcessing;
        private string _processingMessage;
        private double _processingProgress;
        private ImportModeType _importMode;
        private string _searchText;
        private int _errorCount;
        private int _totalPoints;
        private string _fileSelectionStatus;
        private string _lastErrorMessage;
        #endregion

        #region Enums

        /// <summary>
        /// Represents the current step in the import wizard
        /// </summary>
        public enum ImportWizardStep
        {
            Initial,        // Blank slate with [+] Select Files button
            Staging,        // Configuration view with staged files and settings
            Processing,     // Background processing with progress bar
            DataReview      // Main data grid view with manipulation tools
        }

        /// <summary>
        /// Import mode selection
        /// </summary>
        public enum ImportModeType
        {
            Replace,        // Clear existing points and import new ones
            Append          // Add new points to existing collection
        }

        #endregion

        #region Core Properties

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        /// <summary>
        /// Current step in the import wizard
        /// </summary>
        public ImportWizardStep CurrentStep
        {
            get => _currentStep;
            set
            {
                if (SetProperty(ref _currentStep, value))
                {
                    // Notify all step visibility properties
                    OnPropertyChanged(nameof(IsInitialViewVisible));
                    OnPropertyChanged(nameof(IsStagingViewVisible));
                    OnPropertyChanged(nameof(IsProcessingViewVisible));
                    OnPropertyChanged(nameof(IsDataReviewViewVisible));
                }
            }
        }

        /// <summary>
        /// Whether processing is currently running
        /// </summary>
        public bool IsProcessing
        {
            get => _isProcessing;
            set => SetProperty(ref _isProcessing, value);
        }

        /// <summary>
        /// Current processing status message
        /// </summary>
        public string ProcessingMessage
        {
            get => _processingMessage;
            set => SetProperty(ref _processingMessage, value);
        }

        /// <summary>
        /// Processing progress (0-100)
        /// </summary>
        public double ProcessingProgress
        {
            get => _processingProgress;
            set => SetProperty(ref _processingProgress, value);
        }

        /// <summary>
        /// Selected import mode
        /// </summary>
        public ImportModeType ImportMode
        {
            get => _importMode;
            set => SetProperty(ref _importMode, value);
        }

        /// <summary>
        /// Search/filter text for the data grid
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    ApplySearchFilter();
                }
            }
        }

        /// <summary>
        /// Number of points with validation errors
        /// </summary>
        public int ErrorCount
        {
            get => _errorCount;
            private set
            {
                if (SetProperty(ref _errorCount, value))
                {
                    OnPropertyChanged(nameof(HasErrors));
                    OnPropertyChanged(nameof(ErrorNavigatorText));
                    OnPropertyChanged(nameof(IsErrorNavigatorVisible));
                    OnPropertyChanged(nameof(CanExecuteFinalActions));
                }
            }
        }

        /// <summary>
        /// Total number of points loaded
        /// </summary>
        public int TotalPoints
        {
            get => _totalPoints;
            private set
            {
                if (SetProperty(ref _totalPoints, value))
                {
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(CanExecuteFinalActions));
                }
            }
        }

        #endregion

        #region Collections

        /// <summary>
        /// Files selected for import (staging area)
        /// </summary>
        public ObservableCollection<StagedFile> StagedFiles { get; }

        /// <summary>
        /// All imported survey points
        /// </summary>
        public ObservableCollection<SurveyPoint> SurveyPoints { get; }

        /// <summary>
        /// Filtered view of survey points (for search functionality)
        /// </summary>
        public ObservableCollection<SurveyPoint> FilteredSurveyPoints { get; }

        #endregion

        #region Computed Properties

        // Step Visibility Properties
        public bool IsInitialViewVisible => CurrentStep == ImportWizardStep.Initial;
        public bool IsStagingViewVisible => CurrentStep == ImportWizardStep.Staging;
        public bool IsProcessingViewVisible => CurrentStep == ImportWizardStep.Processing;
        public bool IsDataReviewViewVisible => CurrentStep == ImportWizardStep.DataReview;

        // Status Properties
        public bool HasErrors => ErrorCount > 0;
        public bool HasStagedFiles => StagedFiles.Count > 0;
        public string StatusText => $"Total Points: {TotalPoints}";
        public string ErrorNavigatorText => $"⚠ {ErrorCount} Error{(ErrorCount == 1 ? "" : "s")} Found";
        public bool IsErrorNavigatorVisible => ErrorCount > 0;

        public string FileSelectionStatus
        {
            get => _fileSelectionStatus ?? "No files selected";
            set => SetProperty(ref _fileSelectionStatus, value);
        }

        public string LastErrorMessage
        {
            get => _lastErrorMessage;
            set => SetProperty(ref _lastErrorMessage, value);
        }

        public bool HasLastError => !string.IsNullOrEmpty(LastErrorMessage);

        // Button Enable/Disable Logic
        public bool CanLoadPoints => HasStagedFiles && !IsProcessing;
        public bool CanExecuteFinalActions => TotalPoints > 0 && ErrorCount == 0 && !IsProcessing;

        // Import Mode Properties
        public bool IsReplaceMode
        {
            get => ImportMode == ImportModeType.Replace;
            set
            {
                if (value) ImportMode = ImportModeType.Replace;
            }
        }

        public bool IsAppendMode
        {
            get => ImportMode == ImportModeType.Append;
            set
            {
                if (value) ImportMode = ImportModeType.Append;
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to select files for import
        /// </summary>
        public ICommand SelectFilesCommand { get; }

        /// <summary>
        /// Command to remove a file from staging
        /// </summary>
        public ICommand RemoveFileCommand { get; }

        /// <summary>
        /// Command to go back to previous step
        /// </summary>
        public ICommand GoBackCommand { get; }

        /// <summary>
        /// Command to load points from staged files
        /// </summary>
        public ICommand LoadPointsCommand { get; }

        /// <summary>
        /// Command to navigate to next error
        /// </summary>
        public ICommand NavigateToNextErrorCommand { get; }

        /// <summary>
        /// Command to clear search filter
        /// </summary>
        public ICommand ClearSearchCommand { get; }

        /// <summary>
        /// Command to delete selected points
        /// </summary>
        public ICommand DeleteSelectedCommand { get; }

        /// <summary>
        /// Command to delete all points
        /// </summary>
        public ICommand DeleteAllCommand { get; }

        /// <summary>
        /// Command to insert points into AutoCAD
        /// </summary>
        public ICommand InsertPointsCommand { get; }

        /// <summary>
        /// Command to export points
        /// </summary>
        public ICommand ExportPointsCommand { get; }

        /// <summary>
        /// Command to save points to database
        /// </summary>
        public ICommand SavePointsCommand { get; }

        #endregion

        #region Constructor

        public ImportViewModel()
        {
            // Initialize collections
            StagedFiles = new ObservableCollection<StagedFile>();
            SurveyPoints = new ObservableCollection<SurveyPoint>();
            FilteredSurveyPoints = new ObservableCollection<SurveyPoint>();

            // Set initial state
            CurrentStep = ImportWizardStep.Initial;
            ImportMode = ImportModeType.Replace; // Default to replace mode
            ProcessingMessage = "Ready";
            ProcessingProgress = 0;
            SearchText = string.Empty;
            LastErrorMessage = string.Empty;

            // Initialize commands
            SelectFilesCommand = new RelayCommand(ExecuteSelectFiles);
            RemoveFileCommand = new RelayCommand(ExecuteRemoveFile, CanExecuteRemoveFile);
            GoBackCommand = new RelayCommand(ExecuteGoBack, CanExecuteGoBack);
            LoadPointsCommand = new RelayCommand(ExecuteLoadPoints, CanExecuteLoadPoints);
            NavigateToNextErrorCommand = new RelayCommand(ExecuteNavigateToNextError, CanExecuteNavigateToNextError);
            ClearSearchCommand = new RelayCommand(ExecuteClearSearch, CanExecuteClearSearch);
            DeleteSelectedCommand = new RelayCommand(ExecuteDeleteSelected, CanExecuteDeleteSelected);
            DeleteAllCommand = new RelayCommand(ExecuteDeleteAll, CanExecuteDeleteAll);
            InsertPointsCommand = new RelayCommand(ExecuteInsertPoints, CanExecuteInsertPoints);
            ExportPointsCommand = new RelayCommand(ExecuteExportPoints, CanExecuteExportPoints);
            SavePointsCommand = new RelayCommand(ExecuteSavePoints, CanExecuteSavePoints);

            // Subscribe to collection changes for automatic updates
            StagedFiles.CollectionChanged += (s, e) =>
            {
                OnPropertyChanged(nameof(HasStagedFiles));
                OnPropertyChanged(nameof(CanLoadPoints));
            };

            SurveyPoints.CollectionChanged += (s, e) =>
            {
                UpdateTotalPoints();
                UpdateErrorCount();
                ApplySearchFilter();
            };
        }

        #endregion

        #region Command Implementations

        private void ExecuteSelectFiles(object parameter)
        {
            // Clear any previous errors
            ClearLastError();

            try
            {
                // Create and configure the OpenFileDialog
                var openFileDialog = new OpenFileDialog
                {
                    Title = "Select Survey Files to Import",
                    Multiselect = true,
                    CheckFileExists = true,
                    CheckPathExists = true,
                    Filter = CreateFileFilter(),
                    FilterIndex = 1, // Default to "All Supported Files"
                    RestoreDirectory = true
                };

                // Show the dialog
                var result = openFileDialog.ShowDialog();

                if (result == true && openFileDialog.FileNames.Length > 0)
                {
                    // Clear any existing staged files
                    StagedFiles.Clear();

                    // Process each selected file
                    int totalFiles = openFileDialog.FileNames.Length;
                    int validFiles = 0;
                    int invalidFiles = 0;

                    foreach (string filePath in openFileDialog.FileNames)
                    {
                        try
                        {
                            var stagedFile = new StagedFile(filePath);
                            StagedFiles.Add(stagedFile);

                            if (stagedFile.IsValid)
                                validFiles++;
                            else
                                invalidFiles++;
                        }
                        catch (Exception ex)
                        {
                            // Create a staged file with error status
                            var errorFile = new StagedFile(filePath);
                            errorFile.IsValid = false;
                            errorFile.Status = "Error";
                            errorFile.ErrorMessage = ex.Message;
                            StagedFiles.Add(errorFile);
                            invalidFiles++;

                            System.Diagnostics.Debug.WriteLine($"Error processing file {filePath}: {ex.Message}");
                        }
                    }

                    // Update status message
                    if (invalidFiles > 0)
                    {
                        FileSelectionStatus = $"Selected {totalFiles} files: {validFiles} valid, {invalidFiles} with issues";
                    }
                    else
                    {
                        FileSelectionStatus = $"Selected {validFiles} files successfully";
                    }

                    // If we successfully staged at least one file, navigate to staging view
                    if (StagedFiles.Count > 0)
                    {
                        CurrentStep = ImportWizardStep.Staging;
                    }
                }
                // If user cancels or no files selected, stay on initial view
            }
            catch (Exception ex)
            {
                // Handle any unexpected errors
                SetErrorMessage($"File selection failed: {ex.Message}");
                FileSelectionStatus = "File selection failed";

                // Clear any partially loaded files
                StagedFiles.Clear();
            }
        }

        private bool CanExecuteRemoveFile(object parameter)
        {
            return parameter is StagedFile;
        }

        private void ExecuteRemoveFile(object parameter)
        {
            if (parameter is StagedFile file)
            {
                StagedFiles.Remove(file);
            }
        }

        private bool CanExecuteGoBack(object parameter)
        {
            return CurrentStep != ImportWizardStep.Initial && !IsProcessing;
        }

        private void ExecuteGoBack(object parameter)
        {
            switch (CurrentStep)
            {
                case ImportWizardStep.Staging:
                    CurrentStep = ImportWizardStep.Initial;
                    break;
                case ImportWizardStep.Processing:
                    // Cannot go back during processing
                    break;
                case ImportWizardStep.DataReview:
                    CurrentStep = ImportWizardStep.Staging;
                    break;
            }
        }

        private bool CanExecuteLoadPoints(object parameter)
        {
            return CanLoadPoints;
        }

        private void ExecuteLoadPoints(object parameter)
        {
            // TODO: Implement background file processing
            // This will be implemented in Phase 4
            CurrentStep = ImportWizardStep.Processing;
        }

        private bool CanExecuteNavigateToNextError(object parameter)
        {
            return ErrorCount > 0;
        }

        private void ExecuteNavigateToNextError(object parameter)
        {
            // TODO: Implement error navigation
            // This will be implemented in Phase 6
        }

        private bool CanExecuteClearSearch(object parameter)
        {
            return !string.IsNullOrWhiteSpace(SearchText);
        }

        private void ExecuteClearSearch(object parameter)
        {
            SearchText = string.Empty;
        }

        private bool CanExecuteDeleteSelected(object parameter)
        {
            // TODO: Implement when we have selection tracking
            return false;
        }

        private void ExecuteDeleteSelected(object parameter)
        {
            // TODO: Implement in Phase 7
        }

        private bool CanExecuteDeleteAll(object parameter)
        {
            return TotalPoints > 0;
        }

        private void ExecuteDeleteAll(object parameter)
        {
            // TODO: Implement with confirmation dialog in Phase 7
        }

        private bool CanExecuteInsertPoints(object parameter)
        {
            return CanExecuteFinalActions;
        }

        private void ExecuteInsertPoints(object parameter)
        {
            // TODO: Implement AutoCAD integration in Phase 9
        }

        private bool CanExecuteExportPoints(object parameter)
        {
            return CanExecuteFinalActions;
        }

        private void ExecuteExportPoints(object parameter)
        {
            // TODO: Implement export functionality in Phase 9
        }

        private bool CanExecuteSavePoints(object parameter)
        {
            return CanExecuteFinalActions;
        }

        private void ExecuteSavePoints(object parameter)
        {
            // TODO: Implement database save in Phase 9
        }

        #endregion

        #region File Selection Methods

        /// <summary>
        /// Creates a comprehensive file filter for the OpenFileDialog supporting all import formats
        /// </summary>
        private string CreateFileFilter()
        {
            return "All Supported Files|*.csv;*.txt;*.xml;*.dxf;*.dwg;*.shp;*.kml;*.kmz;*.gpx;*.geojson;*.json;*.sdr;*.idx;*.gsi;*.pnt|" +
                   "CSV Files (*.csv)|*.csv|" +
                   "Text Files (*.txt)|*.txt|" +
                   "LandXML Files (*.xml)|*.xml|" +
                   "DXF Files (*.dxf)|*.dxf|" +
                   "DWG Files (*.dwg)|*.dwg|" +
                   "Shapefile (*.shp)|*.shp|" +
                   "KML Files (*.kml)|*.kml|" +
                   "KMZ Files (*.kmz)|*.kmz|" +
                   "GPX Files (*.gpx)|*.gpx|" +
                   "GeoJSON Files (*.geojson;*.json)|*.geojson;*.json|" +
                   "SDR Files (*.sdr)|*.sdr|" +
                   "IDX Files (*.idx)|*.idx|" +
                   "GSI Files (*.gsi)|*.gsi|" +
                   "PNT Files (*.pnt)|*.pnt|" +
                   "All Files (*.*)|*.*";
        }

        /// <summary>
        /// Determines the file format based on file extension
        /// </summary>
        public static string GetFileFormat(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "Unknown";

            string extension = Path.GetExtension(filePath).ToLowerInvariant();

            switch (extension)
            {
                case ".csv":
                    return "CSV";
                case ".txt":
                    return "TXT";
                case ".xml":
                    return "LandXML";
                case ".dxf":
                    return "DXF";
                case ".dwg":
                    return "DWG";
                case ".shp":
                    return "Shapefile";
                case ".kml":
                    return "KML";
                case ".kmz":
                    return "KMZ";
                case ".gpx":
                    return "GPX";
                case ".geojson":
                case ".json":
                    return "GeoJSON";
                case ".sdr":
                    return "SDR";
                case ".idx":
                    return "IDX";
                case ".gsi":
                    return "GSI";
                case ".pnt":
                    return "PNT";
                default:
                    return "Unknown";
            }
        }

        #endregion

        #region Error Handling Methods

        /// <summary>
        /// Clears the last error message
        /// </summary>
        public void ClearLastError()
        {
            LastErrorMessage = string.Empty;
            OnPropertyChanged(nameof(HasLastError));
        }

        /// <summary>
        /// Sets an error message and updates related properties
        /// </summary>
        private void SetErrorMessage(string message)
        {
            LastErrorMessage = message;
            OnPropertyChanged(nameof(HasLastError));
            System.Diagnostics.Debug.WriteLine($"Error: {message}");
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Updates the total points count
        /// </summary>
        private void UpdateTotalPoints()
        {
            TotalPoints = SurveyPoints.Count;
        }

        /// <summary>
        /// Updates the error count by scanning all points
        /// </summary>
        private void UpdateErrorCount()
        {
            ErrorCount = SurveyPoints.Count(p => p.HasError);
        }

        /// <summary>
        /// Applies the current search filter to the points collection
        /// </summary>
        private void ApplySearchFilter()
        {
            FilteredSurveyPoints.Clear();

            if (string.IsNullOrWhiteSpace(SearchText))
            {
                // No filter - show all points
                foreach (var point in SurveyPoints)
                {
                    FilteredSurveyPoints.Add(point);
                }
            }
            else
            {
                // Apply search filter
                var searchLower = SearchText.ToLowerInvariant();
                var filteredPoints = SurveyPoints.Where(p =>
                    (p.PointNumber?.ToLowerInvariant().Contains(searchLower) == true) ||
                    (p.Description?.ToLowerInvariant().Contains(searchLower) == true) ||
                    (p.Easting?.Contains(SearchText) == true) ||
                    (p.Northing?.Contains(SearchText) == true) ||
                    (p.Elevation?.Contains(SearchText) == true));

                foreach (var point in filteredPoints)
                {
                    FilteredSurveyPoints.Add(point);
                }
            }
        }

        /// <summary>
        /// Resets the import wizard to initial state
        /// </summary>
        public void Reset()
        {
            StagedFiles.Clear();
            SurveyPoints.Clear();
            FilteredSurveyPoints.Clear();
            CurrentStep = ImportWizardStep.Initial;
            IsProcessing = false;
            ProcessingMessage = "Ready";
            ProcessingProgress = 0;
            SearchText = string.Empty;
            ErrorCount = 0;
            TotalPoints = 0;
        }

        #endregion
    }

    /// <summary>
    /// Represents a file staged for import
    /// </summary>
    public class StagedFile : ViewModelBase
    {
        private string _filePath;
        private string _fileName;
        private string _fileFormat;
        private long _fileSize;
        private DateTime _lastModified;
        private string _status;
        private bool _isValid;
        private string _errorMessage;

        public string FilePath
        {
            get => _filePath;
            set => SetProperty(ref _filePath, value);
        }

        public string FileName
        {
            get => _fileName;
            set => SetProperty(ref _fileName, value);
        }

        public string FileFormat
        {
            get => _fileFormat;
            set => SetProperty(ref _fileFormat, value);
        }

        public long FileSize
        {
            get => _fileSize;
            set => SetProperty(ref _fileSize, value);
        }

        public string FileSizeText => FormatFileSize(FileSize);

        public DateTime LastModified
        {
            get => _lastModified;
            set => SetProperty(ref _lastModified, value);
        }

        public string LastModifiedText => LastModified.ToString("yyyy-MM-dd HH:mm");

        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public bool IsValid
        {
            get => _isValid;
            set => SetProperty(ref _isValid, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public StagedFile(string filePath)
        {
            FilePath = filePath;
            FileName = Path.GetFileName(filePath);
            FileFormat = ImportViewModel.GetFileFormat(filePath);
            Status = "Ready";
            IsValid = true;
            ErrorMessage = string.Empty;

            try
            {
                var fileInfo = new FileInfo(filePath);
                FileSize = fileInfo.Length;
                LastModified = fileInfo.LastWriteTime;

                // Validate file accessibility
                ValidateFile();
            }
            catch (Exception ex)
            {
                FileSize = 0;
                LastModified = DateTime.MinValue;
                IsValid = false;
                Status = "Error";
                ErrorMessage = $"Cannot access file: {ex.Message}";
            }
        }

        /// <summary>
        /// Validates the file and updates status accordingly
        /// </summary>
        private void ValidateFile()
        {
            try
            {
                // Check if file exists
                if (!File.Exists(FilePath))
                {
                    IsValid = false;
                    Status = "Missing";
                    ErrorMessage = "File not found";
                    return;
                }

                // Check file size
                if (FileSize == 0)
                {
                    IsValid = false;
                    Status = "Empty";
                    ErrorMessage = "File is empty";
                    return;
                }

                // Check if file is too large (>100MB)
                if (FileSize > 100 * 1024 * 1024)
                {
                    IsValid = false;
                    Status = "Too Large";
                    ErrorMessage = "File exceeds 100MB limit";
                    return;
                }

                // Check file format support
                if (FileFormat == "Unknown")
                {
                    IsValid = false;
                    Status = "Unsupported";
                    ErrorMessage = "Unsupported file format";
                    return;
                }

                // Try to open file for reading
                using (var stream = File.OpenRead(FilePath))
                {
                    // File is accessible
                    IsValid = true;
                    Status = "Ready";
                    ErrorMessage = string.Empty;
                }
            }
            catch (UnauthorizedAccessException)
            {
                IsValid = false;
                Status = "Access Denied";
                ErrorMessage = "Access denied - check file permissions";
            }
            catch (IOException ex)
            {
                IsValid = false;
                Status = "I/O Error";
                ErrorMessage = $"I/O error: {ex.Message}";
            }
            catch (Exception ex)
            {
                IsValid = false;
                Status = "Error";
                ErrorMessage = $"Validation error: {ex.Message}";
            }
        }

        private static string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
            return $"{bytes / (1024 * 1024 * 1024):F1} GB";
        }

        public override string ToString()
        {
            return $"{FileName} ({FileSizeText})";
        }
    }
}