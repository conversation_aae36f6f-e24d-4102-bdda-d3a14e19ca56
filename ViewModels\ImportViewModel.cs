using SurveyPointsManager.Infrastructure.Base;
using SurveyPointsManager.Infrastructure.Commands;
using SurveyPointsManager.Infrastructure.Services;
using SurveyPointsManager.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Win32;

namespace SurveyPointsManager.ViewModels
{
    /// <summary>
    /// ViewModel for the Import module with wizard-style navigation and comprehensive state management
    /// </summary>
    public class ImportViewModel : ViewModelBase
    {
        #region Private Fields
        private string _title = "Import";
        private ImportWizardStep _currentStep;
        private bool _isProcessing;
        private string _processingMessage;
        private double _processingProgress;
        private ImportModeType _importMode;
        private string _searchText;
        private int _errorCount;
        private int _totalPoints;
        private string _fileSelectionStatus;
        private string _lastErrorMessage;
        private StagedFile _selectedStagedFile;
        private bool _isConfigurationPopupOpen;
        private readonly FileProcessingService _fileProcessingService;
        private CancellationTokenSource _filterCancellationTokenSource;
        private bool _isUpdatingCollection = false;
        #endregion

        #region Enums

        /// <summary>
        /// Represents the current step in the import wizard
        /// </summary>
        public enum ImportWizardStep
        {
            Initial,        // Blank slate with [+] Select Files button
            Staging,        // Configuration view with staged files and settings
            Processing,     // Background processing with progress bar
            DataReview      // Main data grid view with manipulation tools
        }

        /// <summary>
        /// Import mode selection
        /// </summary>
        public enum ImportModeType
        {
            Replace,        // Clear existing points and import new ones
            Append          // Add new points to existing collection
        }

        #endregion

        #region Core Properties

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        /// <summary>
        /// Current step in the import wizard
        /// </summary>
        public ImportWizardStep CurrentStep
        {
            get => _currentStep;
            set
            {
                if (SetProperty(ref _currentStep, value))
                {
                    // Notify all step visibility properties
                    OnPropertyChanged(nameof(IsInitialViewVisible));
                    OnPropertyChanged(nameof(IsStagingViewVisible));
                    OnPropertyChanged(nameof(IsProcessingViewVisible));
                    OnPropertyChanged(nameof(IsDataReviewViewVisible));
                }
            }
        }

        /// <summary>
        /// Whether processing is currently running
        /// </summary>
        public bool IsProcessing
        {
            get => _isProcessing;
            set => SetProperty(ref _isProcessing, value);
        }

        /// <summary>
        /// Current processing status message
        /// </summary>
        public string ProcessingMessage
        {
            get => _processingMessage;
            set => SetProperty(ref _processingMessage, value);
        }

        /// <summary>
        /// Processing progress (0-100)
        /// </summary>
        public double ProcessingProgress
        {
            get => _processingProgress;
            set => SetProperty(ref _processingProgress, value);
        }

        /// <summary>
        /// Selected import mode
        /// </summary>
        public ImportModeType ImportMode
        {
            get => _importMode;
            set => SetProperty(ref _importMode, value);
        }

        /// <summary>
        /// Search/filter text for the data grid
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    ApplySearchFilter();
                }
            }
        }

        /// <summary>
        /// Number of points with validation errors
        /// </summary>
        public int ErrorCount
        {
            get => _errorCount;
            private set
            {
                if (SetProperty(ref _errorCount, value))
                {
                    OnPropertyChanged(nameof(HasErrors));
                    OnPropertyChanged(nameof(ErrorNavigatorText));
                    OnPropertyChanged(nameof(IsErrorNavigatorVisible));
                    OnPropertyChanged(nameof(CanExecuteFinalActions));
                }
            }
        }

        /// <summary>
        /// Total number of points loaded
        /// </summary>
        public int TotalPoints
        {
            get => _totalPoints;
            private set
            {
                if (SetProperty(ref _totalPoints, value))
                {
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(CanExecuteFinalActions));
                }
            }
        }

        #endregion

        #region Collections

        /// <summary>
        /// Files selected for import (staging area)
        /// </summary>
        public ObservableCollection<StagedFile> StagedFiles { get; }

        /// <summary>
        /// All imported survey points
        /// </summary>
        public ObservableCollection<SurveyPoint> SurveyPoints { get; }

        /// <summary>
        /// Filtered view of survey points (for search functionality)
        /// </summary>
        public ObservableCollection<SurveyPoint> FilteredSurveyPoints { get; }

        #endregion

        #region Computed Properties

        // Step Visibility Properties
        public bool IsInitialViewVisible => CurrentStep == ImportWizardStep.Initial;
        public bool IsStagingViewVisible => CurrentStep == ImportWizardStep.Staging;
        public bool IsProcessingViewVisible => CurrentStep == ImportWizardStep.Processing;
        public bool IsDataReviewViewVisible => CurrentStep == ImportWizardStep.DataReview;

        // Status Properties
        public bool HasErrors => ErrorCount > 0;
        public bool HasStagedFiles => StagedFiles.Count > 0;
        public string StatusText => $"Total Points: {TotalPoints}";
        public string ErrorNavigatorText => $"⚠ {ErrorCount} Error{(ErrorCount == 1 ? "" : "s")} Found";
        public bool IsErrorNavigatorVisible => ErrorCount > 0;

        public string FileSelectionStatus
        {
            get => _fileSelectionStatus ?? "No files selected";
            set => SetProperty(ref _fileSelectionStatus, value);
        }

        public string LastErrorMessage
        {
            get => _lastErrorMessage;
            set => SetProperty(ref _lastErrorMessage, value);
        }

        public bool HasLastError => !string.IsNullOrEmpty(LastErrorMessage);

        public StagedFile SelectedStagedFile
        {
            get => _selectedStagedFile;
            set
            {
                if (SetProperty(ref _selectedStagedFile, value))
                {
                    OnPropertyChanged(nameof(HasSelectedFile));
                    OnPropertyChanged(nameof(SelectedFilePreviewTitle));
                    OnPropertyChanged(nameof(SelectedFilePreviewContent));
                    OnPropertyChanged(nameof(SelectedFileConfigurationContent));
                }
            }
        }

        public bool HasSelectedFile => SelectedStagedFile != null;

        public string SelectedFilePreviewTitle =>
            SelectedStagedFile != null ? $"Preview: {SelectedStagedFile.FileName}" : "Select a file to preview";

        public object SelectedFilePreviewContent =>
            SelectedStagedFile != null ? CreateFilePreview(SelectedStagedFile) : CreateNoSelectionMessage();

        public object SelectedFileConfigurationContent =>
            SelectedStagedFile != null ? CreateFileConfiguration(SelectedStagedFile) : null;

        public object ConfigurationContent =>
            SelectedStagedFile != null ? CreateFileConfiguration(SelectedStagedFile) : null;

        public bool IsConfigurationPopupOpen
        {
            get => _isConfigurationPopupOpen;
            set => SetProperty(ref _isConfigurationPopupOpen, value);
        }

        // Button Enable/Disable Logic
        public bool CanLoadPoints => HasStagedFiles && !IsProcessing;
        public bool CanExecuteFinalActions => TotalPoints > 0 && ErrorCount == 0 && !IsProcessing;

        // Import Mode Properties
        public bool IsReplaceMode
        {
            get => ImportMode == ImportModeType.Replace;
            set
            {
                if (value) ImportMode = ImportModeType.Replace;
            }
        }

        public bool IsAppendMode
        {
            get => ImportMode == ImportModeType.Append;
            set
            {
                if (value) ImportMode = ImportModeType.Append;
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to select files for import
        /// </summary>
        public ICommand SelectFilesCommand { get; }

        /// <summary>
        /// Command to remove a file from staging
        /// </summary>
        public ICommand RemoveFileCommand { get; }

        /// <summary>
        /// Command to configure file import settings
        /// </summary>
        public ICommand ConfigureFileCommand { get; }

        /// <summary>
        /// Command to apply configuration changes
        /// </summary>
        public ICommand ApplyConfigurationCommand { get; set; }

        /// <summary>
        /// Command to cancel configuration changes
        /// </summary>
        public ICommand CancelConfigurationCommand { get; set; }

        /// <summary>
        /// Command to reset configuration to defaults
        /// </summary>
        public ICommand ResetConfigurationCommand { get; set; }

        /// <summary>
        /// Command to add more files from different directories
        /// </summary>
        public ICommand AddMoreFilesCommand { get; }

        /// <summary>
        /// Command to load points from staged files
        /// </summary>
        public ICommand LoadPointsCommand { get; }

        /// <summary>
        /// Command to go back to previous step
        /// </summary>
        public ICommand GoBackCommand { get; }

        /// <summary>
        /// Command to import more files from DataReview step
        /// </summary>
        public ICommand ImportMoreFilesCommand { get; }

        /// <summary>
        /// Command to start a new import session
        /// </summary>
        public ICommand StartNewImportCommand { get; }

        /// <summary>
        /// Command to return to staging for reconfiguration
        /// </summary>
        public ICommand ReturnToStagingCommand { get; }

        /// <summary>
        /// Command to navigate to next error
        /// </summary>
        public ICommand NavigateToNextErrorCommand { get; }

        /// <summary>
        /// Command to clear search filter
        /// </summary>
        public ICommand ClearSearchCommand { get; }

        /// <summary>
        /// Command to delete selected points
        /// </summary>
        public ICommand DeleteSelectedCommand { get; }

        /// <summary>
        /// Command to delete all points
        /// </summary>
        public ICommand DeleteAllCommand { get; }

        /// <summary>
        /// Command to insert points into AutoCAD
        /// </summary>
        public ICommand InsertPointsCommand { get; }

        /// <summary>
        /// Command to export points
        /// </summary>
        public ICommand ExportPointsCommand { get; }

        /// <summary>
        /// Command to save points to database
        /// </summary>
        public ICommand SavePointsCommand { get; }

        #endregion

        #region Constructor

        public ImportViewModel()
        {
            // Initialize collections
            StagedFiles = new ObservableCollection<StagedFile>();
            SurveyPoints = new ObservableCollection<SurveyPoint>();

            // Initialize services
            _fileProcessingService = new FileProcessingService();
            _fileProcessingService.ProgressChanged += OnProcessingProgressChanged;
            _fileProcessingService.ProcessingCompleted += OnProcessingCompleted;
            FilteredSurveyPoints = new ObservableCollection<SurveyPoint>();

            // Set initial state
            CurrentStep = ImportWizardStep.Initial;
            ImportMode = ImportModeType.Replace; // Default to replace mode
            ProcessingMessage = "Ready";
            ProcessingProgress = 0;
            SearchText = string.Empty;
            LastErrorMessage = string.Empty;

            // Initialize commands
            SelectFilesCommand = new RelayCommand(ExecuteSelectFiles);
            RemoveFileCommand = new RelayCommand(ExecuteRemoveFile, CanExecuteRemoveFile);
            ConfigureFileCommand = new RelayCommand(ExecuteConfigureFile, CanExecuteConfigureFile);
            AddMoreFilesCommand = new RelayCommand(ExecuteAddMoreFiles);
            LoadPointsCommand = new RelayCommand(ExecuteLoadPoints, CanExecuteLoadPoints);

            // Popup commands
            ApplyConfigurationCommand = new RelayCommand(ExecuteApplyConfiguration, CanExecuteApplyConfiguration);
            CancelConfigurationCommand = new RelayCommand(ExecuteCancelConfiguration);
            ResetConfigurationCommand = new RelayCommand(ExecuteResetConfiguration, CanExecuteResetConfiguration);

            GoBackCommand = new RelayCommand(ExecuteGoBack, CanExecuteGoBack);

            // Navigation commands for enhanced workflow
            ImportMoreFilesCommand = new RelayCommand(ExecuteImportMoreFiles, CanExecuteImportMoreFiles);
            StartNewImportCommand = new RelayCommand(ExecuteStartNewImport, CanExecuteStartNewImport);
            ReturnToStagingCommand = new RelayCommand(ExecuteReturnToStaging, CanExecuteReturnToStaging);

            NavigateToNextErrorCommand = new RelayCommand(ExecuteNavigateToNextError, CanExecuteNavigateToNextError);
            ClearSearchCommand = new RelayCommand(ExecuteClearSearch, CanExecuteClearSearch);
            DeleteSelectedCommand = new RelayCommand(ExecuteDeleteSelected, CanExecuteDeleteSelected);
            DeleteAllCommand = new RelayCommand(ExecuteDeleteAll, CanExecuteDeleteAll);
            InsertPointsCommand = new RelayCommand(ExecuteInsertPoints, CanExecuteInsertPoints);
            ExportPointsCommand = new RelayCommand(ExecuteExportPoints, CanExecuteExportPoints);
            SavePointsCommand = new RelayCommand(ExecuteSavePoints, CanExecuteSavePoints);

            // Subscribe to collection changes for automatic updates
            StagedFiles.CollectionChanged += (s, e) =>
            {
                OnPropertyChanged(nameof(HasStagedFiles));
                OnPropertyChanged(nameof(CanLoadPoints));
            };

            SurveyPoints.CollectionChanged += async (s, e) =>
            {
                if (_isUpdatingCollection)
                    return;

                // Debounce rapid changes
                await Task.Delay(50);

                UpdateTotalPoints();
                await UpdateErrorCountAsync();
                ApplySearchFilter();
            };
        }

        #endregion

        #region Command Implementations

        private void ExecuteSelectFiles(object parameter)
        {
            // Clear any previous errors
            ClearLastError();

            try
            {
                // Create and configure the OpenFileDialog
                var openFileDialog = new OpenFileDialog
                {
                    Title = "Select Survey Files to Import",
                    Multiselect = true,
                    CheckFileExists = true,
                    CheckPathExists = true,
                    Filter = CreateFileFilter(),
                    FilterIndex = 1, // Default to "All Supported Files"
                    RestoreDirectory = true
                };

                // Show the dialog
                var result = openFileDialog.ShowDialog();

                if (result == true && openFileDialog.FileNames.Length > 0)
                {
                    // Clear any existing staged files
                    StagedFiles.Clear();

                    // Process each selected file
                    int totalFiles = openFileDialog.FileNames.Length;
                    int validFiles = 0;
                    int invalidFiles = 0;

                    foreach (string filePath in openFileDialog.FileNames)
                    {
                        try
                        {
                            var stagedFile = new StagedFile(filePath);
                            StagedFiles.Add(stagedFile);

                            if (stagedFile.IsValid)
                                validFiles++;
                            else
                                invalidFiles++;
                        }
                        catch (Exception ex)
                        {
                            // Create a staged file with error status
                            var errorFile = new StagedFile(filePath);
                            errorFile.IsValid = false;
                            errorFile.Status = "Error";
                            errorFile.ErrorMessage = ex.Message;
                            StagedFiles.Add(errorFile);
                            invalidFiles++;

                            System.Diagnostics.Debug.WriteLine($"Error processing file {filePath}: {ex.Message}");
                        }
                    }

                    // Update status message
                    if (invalidFiles > 0)
                    {
                        FileSelectionStatus = $"Selected {totalFiles} files: {validFiles} valid, {invalidFiles} with issues";
                    }
                    else
                    {
                        FileSelectionStatus = $"Selected {validFiles} files successfully";
                    }

                    // If we successfully staged at least one file, navigate to staging view
                    if (StagedFiles.Count > 0)
                    {
                        CurrentStep = ImportWizardStep.Staging;
                    }
                }
                // If user cancels or no files selected, stay on initial view
            }
            catch (Exception ex)
            {
                // Handle any unexpected errors
                SetErrorMessage($"File selection failed: {ex.Message}");
                FileSelectionStatus = "File selection failed";

                // Clear any partially loaded files
                StagedFiles.Clear();
            }
        }

        private bool CanExecuteRemoveFile(object parameter)
        {
            return parameter is StagedFile;
        }

        private void ExecuteRemoveFile(object parameter)
        {
            if (parameter is StagedFile file)
            {
                StagedFiles.Remove(file);
            }
        }

        private bool CanExecuteGoBack(object parameter)
        {
            return CurrentStep != ImportWizardStep.Initial && !_fileProcessingService.IsProcessing;
        }

        private void ExecuteGoBack(object parameter)
        {
            switch (CurrentStep)
            {
                case ImportWizardStep.Staging:
                    CurrentStep = ImportWizardStep.Initial;
                    break;
                case ImportWizardStep.Processing:
                    // Cannot go back during processing
                    break;
                case ImportWizardStep.DataReview:
                    CurrentStep = ImportWizardStep.Staging;
                    break;
            }
        }



        private bool CanExecuteNavigateToNextError(object parameter)
        {
            return ErrorCount > 0;
        }

        private void ExecuteNavigateToNextError(object parameter)
        {
            // TODO: Implement error navigation
            // This will be implemented in Phase 6
        }

        private bool CanExecuteClearSearch(object parameter)
        {
            return !string.IsNullOrWhiteSpace(SearchText);
        }

        private void ExecuteClearSearch(object parameter)
        {
            SearchText = string.Empty;
        }

        private bool CanExecuteDeleteSelected(object parameter)
        {
            // TODO: Implement when we have selection tracking
            return false;
        }

        private void ExecuteDeleteSelected(object parameter)
        {
            // TODO: Implement in Phase 7
        }

        private bool CanExecuteDeleteAll(object parameter)
        {
            return TotalPoints > 0;
        }

        private void ExecuteDeleteAll(object parameter)
        {
            // TODO: Implement with confirmation dialog in Phase 7
        }

        private bool CanExecuteInsertPoints(object parameter)
        {
            return CanExecuteFinalActions;
        }

        private void ExecuteInsertPoints(object parameter)
        {
            // TODO: Implement AutoCAD integration in Phase 9
        }

        private bool CanExecuteExportPoints(object parameter)
        {
            return CanExecuteFinalActions;
        }

        private void ExecuteExportPoints(object parameter)
        {
            // TODO: Implement export functionality in Phase 9
        }

        private bool CanExecuteSavePoints(object parameter)
        {
            return CanExecuteFinalActions;
        }

        private void ExecuteSavePoints(object parameter)
        {
            // TODO: Implement database save in Phase 9
        }

        #endregion

        #region File Selection Methods

        /// <summary>
        /// Creates a comprehensive file filter for the OpenFileDialog supporting all import formats
        /// </summary>
        private string CreateFileFilter()
        {
            return "All Supported Files|*.csv;*.txt;*.xml;*.dxf;*.dwg;*.shp;*.kml;*.kmz;*.gpx;*.geojson;*.json;*.sdr;*.idx;*.gsi;*.pnt|" +
                   "CSV Files (*.csv)|*.csv|" +
                   "Text Files (*.txt)|*.txt|" +
                   "LandXML Files (*.xml)|*.xml|" +
                   "DXF Files (*.dxf)|*.dxf|" +
                   "DWG Files (*.dwg)|*.dwg|" +
                   "Shapefile (*.shp)|*.shp|" +
                   "KML Files (*.kml)|*.kml|" +
                   "KMZ Files (*.kmz)|*.kmz|" +
                   "GPX Files (*.gpx)|*.gpx|" +
                   "GeoJSON Files (*.geojson;*.json)|*.geojson;*.json|" +
                   "SDR Files (*.sdr)|*.sdr|" +
                   "IDX Files (*.idx)|*.idx|" +
                   "GSI Files (*.gsi)|*.gsi|" +
                   "PNT Files (*.pnt)|*.pnt|" +
                   "All Files (*.*)|*.*";
        }

        /// <summary>
        /// Determines the file format based on file extension
        /// </summary>
        public static string GetFileFormat(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "Unknown";

            string extension = Path.GetExtension(filePath).ToLowerInvariant();

            switch (extension)
            {
                case ".csv":
                    return "CSV";
                case ".txt":
                    return "TXT";
                case ".xml":
                    return "LandXML";
                case ".dxf":
                    return "DXF";
                case ".dwg":
                    return "DWG";
                case ".shp":
                    return "Shapefile";
                case ".kml":
                    return "KML";
                case ".kmz":
                    return "KMZ";
                case ".gpx":
                    return "GPX";
                case ".geojson":
                case ".json":
                    return "GeoJSON";
                case ".sdr":
                    return "SDR";
                case ".idx":
                    return "IDX";
                case ".gsi":
                    return "GSI";
                case ".pnt":
                    return "PNT";
                default:
                    return "Unknown";
            }
        }

        private void ExecuteConfigureFile(object parameter)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"ExecuteConfigureFile called with parameter: {parameter}");

                if (parameter is StagedFile stagedFile)
                {
                    System.Diagnostics.Debug.WriteLine($"Opening configuration popup for: {stagedFile.FileName}");

                    // Set the selected file
                    SelectedStagedFile = stagedFile;

                    // Refresh configuration content
                    OnPropertyChanged(nameof(ConfigurationContent));

                    // Open the popup
                    IsConfigurationPopupOpen = true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Parameter is not a StagedFile. Type: {parameter?.GetType().Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ExecuteConfigureFile: {ex.Message}");
                SetErrorMessage($"Error opening configuration: {ex.Message}");
            }
        }

        private bool CanExecuteConfigureFile(object parameter)
        {
            bool canExecute = parameter is StagedFile;
            System.Diagnostics.Debug.WriteLine($"CanExecuteConfigureFile: {canExecute} for parameter: {parameter}");
            return canExecute;
        }

        private void ExecuteAddMoreFiles(object parameter)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ExecuteAddMoreFiles called");

                var openFileDialog = new OpenFileDialog
                {
                    Title = "Add More Files to Import",
                    Filter = CreateFileFilter(),
                    Multiselect = true,
                    CheckFileExists = true,
                    CheckPathExists = true
                };

                // Set initial directory to last used directory if available
                if (StagedFiles.Count > 0)
                {
                    var lastFile = StagedFiles[StagedFiles.Count - 1];
                    var lastDirectory = System.IO.Path.GetDirectoryName(lastFile.FilePath);
                    if (System.IO.Directory.Exists(lastDirectory))
                    {
                        openFileDialog.InitialDirectory = lastDirectory;
                    }
                }

                if (openFileDialog.ShowDialog() == true)
                {
                    int addedCount = 0;
                    int duplicateCount = 0;

                    foreach (string fileName in openFileDialog.FileNames)
                    {
                        // Check if file is already staged
                        bool isDuplicate = StagedFiles.Any(sf =>
                            string.Equals(sf.FilePath, fileName, StringComparison.OrdinalIgnoreCase));

                        if (!isDuplicate)
                        {
                            var stagedFile = new StagedFile(fileName);
                            StagedFiles.Add(stagedFile);
                            addedCount++;
                            System.Diagnostics.Debug.WriteLine($"Added file: {fileName}");
                        }
                        else
                        {
                            duplicateCount++;
                            System.Diagnostics.Debug.WriteLine($"Skipped duplicate file: {fileName}");
                        }
                    }

                    // Update UI properties
                    OnPropertyChanged(nameof(HasStagedFiles));
                    OnPropertyChanged(nameof(CanLoadPoints));

                    // Provide user feedback
                    if (addedCount > 0)
                    {
                        FileSelectionStatus = $"Added {addedCount} more file(s). Total: {StagedFiles.Count} files selected.";
                        if (duplicateCount > 0)
                        {
                            FileSelectionStatus += $" ({duplicateCount} duplicate(s) skipped)";
                        }
                    }
                    else if (duplicateCount > 0)
                    {
                        FileSelectionStatus = $"All {duplicateCount} selected file(s) were already added.";
                    }

                    System.Diagnostics.Debug.WriteLine($"Added {addedCount} files, skipped {duplicateCount} duplicates. Total staged: {StagedFiles.Count}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ExecuteAddMoreFiles: {ex.Message}");
                SetErrorMessage($"Error adding more files: {ex.Message}");
            }
        }

        private void ExecuteApplyConfiguration(object parameter)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Configuration applied");
                IsConfigurationPopupOpen = false;

                // Refresh any dependent properties
                OnPropertyChanged(nameof(SelectedFilePreviewContent));
                OnPropertyChanged(nameof(SelectedFileConfigurationContent));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying configuration: {ex.Message}");
                SetErrorMessage($"Error applying configuration: {ex.Message}");
            }
        }

        private bool CanExecuteApplyConfiguration(object parameter)
        {
            return SelectedStagedFile != null;
        }

        private void ExecuteCancelConfiguration(object parameter)
        {
            System.Diagnostics.Debug.WriteLine("Configuration cancelled");
            IsConfigurationPopupOpen = false;
        }

        private void ExecuteResetConfiguration(object parameter)
        {
            try
            {
                if (SelectedStagedFile != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Resetting configuration for: {SelectedStagedFile.FileName}");

                    // Reset to defaults
                    var config = SelectedStagedFile.ImportConfiguration;

                    // Reset based on file format
                    switch (SelectedStagedFile.FileFormat)
                    {
                        case "CSV":
                            config.Delimiter = ",";
                            config.HasHeaderRow = true;
                            break;
                        case "TXT":
                            config.Delimiter = "\t";
                            config.HasHeaderRow = false;
                            break;
                        default:
                            config.Delimiter = ",";
                            config.HasHeaderRow = true;
                            break;
                    }

                    config.SkipRows = 0;
                    config.PointNumberColumn = "Point";
                    config.NorthingColumn = "Northing";
                    config.EastingColumn = "Easting";
                    config.ElevationColumn = "Elevation";
                    config.DescriptionColumn = "Description";
                    config.CoordinateSystem = "Local";
                    config.ScaleFactor = 1.0;
                    config.ValidateCoordinates = true;
                    config.AllowDuplicatePoints = false;

                    // Re-detect columns
                    SelectedStagedFile.DetectColumns();

                    // Refresh configuration content
                    OnPropertyChanged(nameof(ConfigurationContent));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error resetting configuration: {ex.Message}");
                SetErrorMessage($"Error resetting configuration: {ex.Message}");
            }
        }

        private bool CanExecuteResetConfiguration(object parameter)
        {
            return SelectedStagedFile != null;
        }

        private async void ExecuteLoadPoints(object parameter)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ExecuteLoadPoints called");

                if (!HasStagedFiles)
                {
                    SetErrorMessage("No files selected for import");
                    return;
                }

                // Clear any previous errors
                ClearLastError();

                // Switch to processing view
                CurrentStep = ImportWizardStep.Processing;

                // Start background processing
                await _fileProcessingService.ProcessFilesAsync(StagedFiles, ImportMode);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ExecuteLoadPoints: {ex.Message}");
                SetErrorMessage($"Error starting import: {ex.Message}");

                // Return to staging view on error
                CurrentStep = ImportWizardStep.Staging;
            }
        }

        private bool CanExecuteLoadPoints(object parameter)
        {
            return HasStagedFiles && !_fileProcessingService.IsProcessing;
        }

        private void OnProcessingProgressChanged(object sender, ProcessingProgressEventArgs e)
        {
            // Update UI with processing progress
            // This will be handled by the ProcessingView
            System.Diagnostics.Debug.WriteLine($"Processing progress: {e.Progress.OverallProgress:F1}% - {e.Progress.CurrentOperation}");
        }

        private async void OnProcessingCompleted(object sender, ProcessingCompletedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Processing completed. Success: {e.Success}, Points: {e.Points.Count}");

                if (e.Success && e.Points.Count > 0)
                {
                    // Handle import mode
                    if (e.ImportMode == ImportModeType.Replace)
                    {
                        SurveyPoints.Clear();
                    }

                    // Add new points efficiently
                    await AddPointsAsync(e.Points);

                    // Update total points count
                    OnPropertyChanged(nameof(TotalPoints));

                    // Switch to data review view
                    CurrentStep = ImportWizardStep.DataReview;

                    // Update status
                    FileSelectionStatus = $"Successfully imported {e.Points.Count} points from {e.Statistics.SuccessfulFiles} file(s)";
                }
                else
                {
                    // Handle errors
                    var errorMessage = e.Success ? "No points were found in the selected files" : "Import failed";
                    if (e.Errors.Any())
                    {
                        errorMessage += $". {e.Errors.Count} error(s) occurred.";
                    }

                    SetErrorMessage(errorMessage);

                    // Return to staging view
                    CurrentStep = ImportWizardStep.Staging;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in OnProcessingCompleted: {ex.Message}");
                SetErrorMessage($"Error completing import: {ex.Message}");
                CurrentStep = ImportWizardStep.Staging;
            }
        }

        private void ExecuteImportMoreFiles(object parameter)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ExecuteImportMoreFiles called - returning to staging to add more files");

                // Keep existing points and staged files, just return to staging
                CurrentStep = ImportWizardStep.Staging;

                // Update status to indicate we're adding more files
                FileSelectionStatus = $"Current: {SurveyPoints.Count} points loaded. Add more files to continue importing.";

                // Clear any error messages
                ClearLastError();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ExecuteImportMoreFiles: {ex.Message}");
                SetErrorMessage($"Error returning to file selection: {ex.Message}");
            }
        }

        private bool CanExecuteImportMoreFiles(object parameter)
        {
            return CurrentStep == ImportWizardStep.DataReview && !_fileProcessingService.IsProcessing;
        }

        private void ExecuteStartNewImport(object parameter)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ExecuteStartNewImport called - starting fresh import session");

                // Clear all existing data
                StagedFiles.Clear();
                SurveyPoints.Clear();
                FilteredSurveyPoints.Clear();

                // Reset properties
                SelectedStagedFile = null;
                SearchText = string.Empty;
                FileSelectionStatus = "Ready to select files for import";

                // Clear errors
                ClearLastError();

                // Reset to initial step
                CurrentStep = ImportWizardStep.Initial;

                // Update UI properties
                OnPropertyChanged(nameof(HasStagedFiles));
                OnPropertyChanged(nameof(TotalPoints));
                OnPropertyChanged(nameof(CanLoadPoints));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ExecuteStartNewImport: {ex.Message}");
                SetErrorMessage($"Error starting new import: {ex.Message}");
            }
        }

        private bool CanExecuteStartNewImport(object parameter)
        {
            return CurrentStep == ImportWizardStep.DataReview && !_fileProcessingService.IsProcessing;
        }

        private void ExecuteReturnToStaging(object parameter)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ExecuteReturnToStaging called - returning to staging for reconfiguration");

                // Keep existing staged files but allow reconfiguration
                CurrentStep = ImportWizardStep.Staging;

                // Update status
                FileSelectionStatus = $"Reconfigure files and settings. Current: {StagedFiles.Count} file(s) staged.";

                // Clear any error messages
                ClearLastError();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ExecuteReturnToStaging: {ex.Message}");
                SetErrorMessage($"Error returning to staging: {ex.Message}");
            }
        }

        private bool CanExecuteReturnToStaging(object parameter)
        {
            return CurrentStep == ImportWizardStep.DataReview && HasStagedFiles && !_fileProcessingService.IsProcessing;
        }

        #endregion

        #region File Preview and Configuration Methods

        /// <summary>
        /// Creates a preview control for the selected file
        /// </summary>
        private object CreateFilePreview(StagedFile stagedFile)
        {
            try
            {
                // Create a text block that will be populated asynchronously
                var preview = new System.Windows.Controls.TextBlock
                {
                    Text = "Loading preview...",
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 11,
                    Margin = new System.Windows.Thickness(8),
                    TextWrapping = System.Windows.TextWrapping.Wrap
                };

                // Load preview asynchronously without blocking UI
                _ = LoadFilePreviewAsync(preview, stagedFile);

                return preview;
            }
            catch (Exception ex)
            {
                return new System.Windows.Controls.TextBlock
                {
                    Text = $"Preview not available: {ex.Message}",
                    FontStyle = System.Windows.FontStyles.Italic,
                    Foreground = System.Windows.Media.Brushes.Red,
                    Margin = new System.Windows.Thickness(8)
                };
            }
        }

        private async Task LoadFilePreviewAsync(System.Windows.Controls.TextBlock textBlock, StagedFile stagedFile)
        {
            try
            {
                var previewText = await GetFilePreviewTextAsync(stagedFile);

                // Update UI on the UI thread
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    textBlock.Text = previewText;
                });
            }
            catch (Exception ex)
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    textBlock.Text = $"Error loading preview: {ex.Message}";
                    textBlock.Foreground = System.Windows.Media.Brushes.Red;
                });
            }
        }

        /// <summary>
        /// Creates a message for when no file is selected
        /// </summary>
        private object CreateNoSelectionMessage()
        {
            return new System.Windows.Controls.TextBlock
            {
                Text = "Select a file from the list to see its preview and configuration options.",
                FontStyle = System.Windows.FontStyles.Italic,
                Foreground = System.Windows.Media.Brushes.Gray,
                Margin = new System.Windows.Thickness(8),
                TextAlignment = System.Windows.TextAlignment.Center,
                VerticalAlignment = System.Windows.VerticalAlignment.Center
            };
        }

        /// <summary>
        /// Creates configuration controls for the selected file
        /// </summary>
        private object CreateFileConfiguration(StagedFile stagedFile)
        {
            var panel = new System.Windows.Controls.StackPanel
            {
                Margin = new System.Windows.Thickness(8)
            };

            // File format specific configuration
            switch (stagedFile.FileFormat)
            {
                case "CSV":
                case "TXT":
                    panel.Children.Add(CreateTextFileConfiguration(stagedFile));
                    break;
                case "LandXML":
                    panel.Children.Add(CreateXmlFileConfiguration(stagedFile));
                    break;
                default:
                    panel.Children.Add(new System.Windows.Controls.TextBlock
                    {
                        Text = $"Configuration options for {stagedFile.FileFormat} files will be available in future updates.",
                        FontStyle = System.Windows.FontStyles.Italic,
                        Foreground = System.Windows.Media.Brushes.Gray,
                        TextWrapping = System.Windows.TextWrapping.Wrap
                    });
                    break;
            }

            return panel;
        }

        /// <summary>
        /// Gets preview text for a file (first few lines) asynchronously
        /// </summary>
        private async Task<string> GetFilePreviewTextAsync(StagedFile stagedFile)
        {
            try
            {
                var lines = await Task.Run(() =>
                    System.IO.File.ReadLines(stagedFile.FilePath).Take(10).ToArray());
                var preview = string.Join(Environment.NewLine, lines);

                if (lines.Length == 10)
                {
                    preview += Environment.NewLine + "... (showing first 10 lines)";
                }

                return preview;
            }
            catch (Exception ex)
            {
                return $"Error reading file: {ex.Message}";
            }
        }

        /// <summary>
        /// Creates configuration controls for text-based files (CSV, TXT)
        /// </summary>
        private System.Windows.Controls.UserControl CreateTextFileConfiguration(StagedFile stagedFile)
        {
            var config = stagedFile.ImportConfiguration;
            var panel = new System.Windows.Controls.StackPanel();

            // Delimiter selection
            panel.Children.Add(new System.Windows.Controls.TextBlock
            {
                Text = "Delimiter:",
                FontWeight = System.Windows.FontWeights.SemiBold,
                Margin = new System.Windows.Thickness(0, 8, 0, 4)
            });

            var delimiterCombo = new System.Windows.Controls.ComboBox
            {
                ItemsSource = config.AvailableDelimiters,
                SelectedItem = config.Delimiter,
                Margin = new System.Windows.Thickness(0, 0, 0, 8)
            };
            delimiterCombo.SelectionChanged += (s, e) => config.Delimiter = delimiterCombo.SelectedItem?.ToString() ?? ",";
            panel.Children.Add(delimiterCombo);

            // Header row checkbox
            var headerCheckBox = new System.Windows.Controls.CheckBox
            {
                Content = "File has header row",
                IsChecked = config.HasHeaderRow,
                Margin = new System.Windows.Thickness(0, 0, 0, 8)
            };
            headerCheckBox.Checked += (s, e) => config.HasHeaderRow = true;
            headerCheckBox.Unchecked += (s, e) => config.HasHeaderRow = false;
            panel.Children.Add(headerCheckBox);

            // Skip rows
            panel.Children.Add(new System.Windows.Controls.TextBlock
            {
                Text = "Skip rows:",
                FontWeight = System.Windows.FontWeights.SemiBold,
                Margin = new System.Windows.Thickness(0, 8, 0, 4)
            });

            var skipRowsBox = new System.Windows.Controls.TextBox
            {
                Text = config.SkipRows.ToString(),
                Margin = new System.Windows.Thickness(0, 0, 0, 8)
            };
            skipRowsBox.TextChanged += (s, e) =>
            {
                if (int.TryParse(skipRowsBox.Text, out int value))
                    config.SkipRows = value;
            };
            panel.Children.Add(skipRowsBox);

            // Column mappings
            panel.Children.Add(new System.Windows.Controls.TextBlock
            {
                Text = "Column Mappings:",
                FontWeight = System.Windows.FontWeights.SemiBold,
                Margin = new System.Windows.Thickness(0, 16, 0, 8)
            });

            // Create column mapping controls
            panel.Children.Add(CreateColumnMappingControl("Point Number:", config.PointNumberColumn, config.AvailableColumns,
                value => config.PointNumberColumn = value));
            panel.Children.Add(CreateColumnMappingControl("Northing:", config.NorthingColumn, config.AvailableColumns,
                value => config.NorthingColumn = value));
            panel.Children.Add(CreateColumnMappingControl("Easting:", config.EastingColumn, config.AvailableColumns,
                value => config.EastingColumn = value));
            panel.Children.Add(CreateColumnMappingControl("Elevation:", config.ElevationColumn, config.AvailableColumns,
                value => config.ElevationColumn = value));
            panel.Children.Add(CreateColumnMappingControl("Description:", config.DescriptionColumn, config.AvailableColumns,
                value => config.DescriptionColumn = value));

            return new System.Windows.Controls.UserControl { Content = panel };
        }

        /// <summary>
        /// Creates a column mapping control
        /// </summary>
        private System.Windows.Controls.StackPanel CreateColumnMappingControl(string label, string selectedValue,
            ObservableCollection<string> availableColumns, Action<string> onSelectionChanged)
        {
            var panel = new System.Windows.Controls.StackPanel
            {
                Orientation = System.Windows.Controls.Orientation.Horizontal,
                Margin = new System.Windows.Thickness(0, 2, 0, 2)
            };

            var labelBlock = new System.Windows.Controls.TextBlock
            {
                Text = label,
                Width = 80,
                VerticalAlignment = System.Windows.VerticalAlignment.Center,
                Margin = new System.Windows.Thickness(0, 0, 8, 0)
            };
            panel.Children.Add(labelBlock);

            var combo = new System.Windows.Controls.ComboBox
            {
                Width = 150,
                ItemsSource = availableColumns,
                SelectedItem = selectedValue
            };
            combo.SelectionChanged += (s, e) => onSelectionChanged?.Invoke(combo.SelectedItem?.ToString() ?? "");
            panel.Children.Add(combo);

            return panel;
        }

        /// <summary>
        /// Creates configuration controls for XML files
        /// </summary>
        private System.Windows.Controls.UserControl CreateXmlFileConfiguration(StagedFile stagedFile)
        {
            var config = stagedFile.ImportConfiguration;
            var panel = new System.Windows.Controls.StackPanel();

            // Coordinate system selection
            panel.Children.Add(new System.Windows.Controls.TextBlock
            {
                Text = "Coordinate System:",
                FontWeight = System.Windows.FontWeights.SemiBold,
                Margin = new System.Windows.Thickness(0, 8, 0, 4)
            });

            var coordSystemCombo = new System.Windows.Controls.ComboBox
            {
                ItemsSource = config.AvailableCoordinateSystems,
                SelectedItem = config.CoordinateSystem,
                Margin = new System.Windows.Thickness(0, 0, 0, 8)
            };
            coordSystemCombo.SelectionChanged += (s, e) => config.CoordinateSystem = coordSystemCombo.SelectedItem?.ToString() ?? "Local";
            panel.Children.Add(coordSystemCombo);

            // Scale factor
            panel.Children.Add(new System.Windows.Controls.TextBlock
            {
                Text = "Scale Factor:",
                FontWeight = System.Windows.FontWeights.SemiBold,
                Margin = new System.Windows.Thickness(0, 8, 0, 4)
            });

            var scaleBox = new System.Windows.Controls.TextBox
            {
                Text = config.ScaleFactor.ToString("F3"),
                Margin = new System.Windows.Thickness(0, 0, 0, 8)
            };
            scaleBox.TextChanged += (s, e) =>
            {
                if (double.TryParse(scaleBox.Text, out double value))
                    config.ScaleFactor = value;
            };
            panel.Children.Add(scaleBox);

            // Validation options
            var validateCheckBox = new System.Windows.Controls.CheckBox
            {
                Content = "Validate coordinates",
                IsChecked = config.ValidateCoordinates,
                Margin = new System.Windows.Thickness(0, 8, 0, 4)
            };
            validateCheckBox.Checked += (s, e) => config.ValidateCoordinates = true;
            validateCheckBox.Unchecked += (s, e) => config.ValidateCoordinates = false;
            panel.Children.Add(validateCheckBox);

            var duplicatesCheckBox = new System.Windows.Controls.CheckBox
            {
                Content = "Allow duplicate points",
                IsChecked = config.AllowDuplicatePoints,
                Margin = new System.Windows.Thickness(0, 4, 0, 8)
            };
            duplicatesCheckBox.Checked += (s, e) => config.AllowDuplicatePoints = true;
            duplicatesCheckBox.Unchecked += (s, e) => config.AllowDuplicatePoints = false;
            panel.Children.Add(duplicatesCheckBox);

            return new System.Windows.Controls.UserControl { Content = panel };
        }

        #endregion

        #region Error Handling Methods

        /// <summary>
        /// Clears the last error message
        /// </summary>
        public void ClearLastError()
        {
            LastErrorMessage = string.Empty;
            OnPropertyChanged(nameof(HasLastError));
        }

        /// <summary>
        /// Sets an error message and updates related properties
        /// </summary>
        private void SetErrorMessage(string message)
        {
            LastErrorMessage = message;
            OnPropertyChanged(nameof(HasLastError));
            System.Diagnostics.Debug.WriteLine($"Error: {message}");
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Updates the total points count
        /// </summary>
        private void UpdateTotalPoints()
        {
            TotalPoints = SurveyPoints.Count;
        }

        /// <summary>
        /// Updates the error count by scanning all points
        /// </summary>
        private void UpdateErrorCount()
        {
            ErrorCount = SurveyPoints.Count(p => p.HasError);
        }

        /// <summary>
        /// Updates the error count by scanning all points asynchronously
        /// </summary>
        private async Task UpdateErrorCountAsync()
        {
            var errorCount = await Task.Run(() => SurveyPoints.Count(p => p.HasError));
            ErrorCount = errorCount;
        }

        /// <summary>
        /// Adds multiple points efficiently without triggering collection change events for each point
        /// </summary>
        public async Task AddPointsAsync(IEnumerable<SurveyPoint> points)
        {
            _isUpdatingCollection = true;

            try
            {
                foreach (var point in points)
                {
                    SurveyPoints.Add(point);
                }
            }
            finally
            {
                _isUpdatingCollection = false;
            }

            // Trigger updates once after all points are added
            UpdateTotalPoints();
            await UpdateErrorCountAsync();
            ApplySearchFilter();
        }

        /// <summary>
        /// Applies the current search filter to the points collection asynchronously
        /// </summary>
        private async void ApplySearchFilter()
        {
            // Cancel previous filter operation
            _filterCancellationTokenSource?.Cancel();
            _filterCancellationTokenSource = new CancellationTokenSource();

            var cancellationToken = _filterCancellationTokenSource.Token;

            try
            {
                var filteredPoints = await Task.Run(() =>
                {
                    if (string.IsNullOrWhiteSpace(SearchText))
                    {
                        return SurveyPoints.ToList();
                    }
                    else
                    {
                        var searchLower = SearchText.ToLowerInvariant();
                        return SurveyPoints.Where(p =>
                            (p.PointNumber?.ToLowerInvariant().Contains(searchLower) == true) ||
                            (p.Description?.ToLowerInvariant().Contains(searchLower) == true) ||
                            (p.Easting?.Contains(SearchText) == true) ||
                            (p.Northing?.Contains(SearchText) == true) ||
                            (p.Elevation?.Contains(SearchText) == true)).ToList();
                    }
                }, cancellationToken);

                if (!cancellationToken.IsCancellationRequested)
                {
                    FilteredSurveyPoints.Clear();
                    foreach (var point in filteredPoints)
                    {
                        FilteredSurveyPoints.Add(point);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Filter was cancelled, ignore
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ApplySearchFilter: {ex.Message}");
            }
        }

        /// <summary>
        /// Resets the import wizard to initial state
        /// </summary>
        public void Reset()
        {
            StagedFiles.Clear();
            SurveyPoints.Clear();
            FilteredSurveyPoints.Clear();
            CurrentStep = ImportWizardStep.Initial;
            IsProcessing = false;
            ProcessingMessage = "Ready";
            ProcessingProgress = 0;
            SearchText = string.Empty;
            ErrorCount = 0;
            TotalPoints = 0;
        }

        #endregion
    }

    /// <summary>
    /// Configuration settings for importing a specific file
    /// </summary>
    public class FileImportConfiguration : ViewModelBase
    {
        private string _delimiter = ",";
        private bool _hasHeaderRow = true;
        private int _skipRows = 0;
        private string _pointNumberColumn = "Point";
        private string _northingColumn = "Northing";
        private string _eastingColumn = "Easting";
        private string _elevationColumn = "Elevation";
        private string _descriptionColumn = "Description";
        private string _coordinateSystem = "Local";
        private double _scaleFactor = 1.0;
        private bool _validateCoordinates = true;
        private bool _allowDuplicatePoints = false;

        public string Delimiter
        {
            get => _delimiter;
            set => SetProperty(ref _delimiter, value);
        }

        public bool HasHeaderRow
        {
            get => _hasHeaderRow;
            set => SetProperty(ref _hasHeaderRow, value);
        }

        public int SkipRows
        {
            get => _skipRows;
            set => SetProperty(ref _skipRows, Math.Max(0, value));
        }

        public string PointNumberColumn
        {
            get => _pointNumberColumn;
            set => SetProperty(ref _pointNumberColumn, value);
        }

        public string NorthingColumn
        {
            get => _northingColumn;
            set => SetProperty(ref _northingColumn, value);
        }

        public string EastingColumn
        {
            get => _eastingColumn;
            set => SetProperty(ref _eastingColumn, value);
        }

        public string ElevationColumn
        {
            get => _elevationColumn;
            set => SetProperty(ref _elevationColumn, value);
        }

        public string DescriptionColumn
        {
            get => _descriptionColumn;
            set => SetProperty(ref _descriptionColumn, value);
        }

        public string CoordinateSystem
        {
            get => _coordinateSystem;
            set => SetProperty(ref _coordinateSystem, value);
        }

        public double ScaleFactor
        {
            get => _scaleFactor;
            set => SetProperty(ref _scaleFactor, Math.Max(0.001, value));
        }

        public bool ValidateCoordinates
        {
            get => _validateCoordinates;
            set => SetProperty(ref _validateCoordinates, value);
        }

        public bool AllowDuplicatePoints
        {
            get => _allowDuplicatePoints;
            set => SetProperty(ref _allowDuplicatePoints, value);
        }

        /// <summary>
        /// Gets available column names from the file
        /// </summary>
        public ObservableCollection<string> AvailableColumns { get; } = new ObservableCollection<string>();

        /// <summary>
        /// Gets available coordinate systems
        /// </summary>
        public ObservableCollection<string> AvailableCoordinateSystems { get; } = new ObservableCollection<string>
        {
            "Local",
            "State Plane",
            "UTM",
            "Geographic (Lat/Lon)",
            "Custom"
        };

        /// <summary>
        /// Gets available delimiters for text files
        /// </summary>
        public ObservableCollection<string> AvailableDelimiters { get; } = new ObservableCollection<string>
        {
            ",", ";", "\t", "|", " "
        };
    }

    /// <summary>
    /// Represents a file staged for import
    /// </summary>
    public class StagedFile : ViewModelBase
    {
        private string _filePath;
        private string _fileName;
        private string _fileFormat;
        private long _fileSize;
        private DateTime _lastModified;
        private string _status;
        private bool _isValid;
        private string _errorMessage;
        private FileImportConfiguration _importConfiguration;

        public string FilePath
        {
            get => _filePath;
            set => SetProperty(ref _filePath, value);
        }

        public string FileName
        {
            get => _fileName;
            set => SetProperty(ref _fileName, value);
        }

        public string FileFormat
        {
            get => _fileFormat;
            set => SetProperty(ref _fileFormat, value);
        }

        public long FileSize
        {
            get => _fileSize;
            set => SetProperty(ref _fileSize, value);
        }

        public string FileSizeText => FormatFileSize(FileSize);

        public DateTime LastModified
        {
            get => _lastModified;
            set => SetProperty(ref _lastModified, value);
        }

        public string LastModifiedText => LastModified.ToString("yyyy-MM-dd HH:mm");

        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public bool IsValid
        {
            get => _isValid;
            set => SetProperty(ref _isValid, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public FileImportConfiguration ImportConfiguration
        {
            get => _importConfiguration ?? (_importConfiguration = CreateDefaultConfiguration());
            set => SetProperty(ref _importConfiguration, value);
        }

        public bool HasError => !IsValid && !string.IsNullOrEmpty(ErrorMessage);

        public StagedFile(string filePath)
        {
            FilePath = filePath;
            FileName = Path.GetFileName(filePath);
            FileFormat = ImportViewModel.GetFileFormat(filePath);
            Status = "Validating...";
            IsValid = false; // Will be set after async validation
            ErrorMessage = string.Empty;

            // Start async validation without blocking constructor
            _ = ValidateFileAsync();
        }

        /// <summary>
        /// Validates the file and updates status accordingly asynchronously
        /// </summary>
        private async Task ValidateFileAsync()
        {
            try
            {
                // Get file info asynchronously
                var fileInfo = await Task.Run(() => new FileInfo(FilePath));

                if (!fileInfo.Exists)
                {
                    IsValid = false;
                    Status = "Missing";
                    ErrorMessage = "File not found";
                    return;
                }

                FileSize = fileInfo.Length;
                LastModified = fileInfo.LastWriteTime;

                // Check file size
                if (FileSize == 0)
                {
                    IsValid = false;
                    Status = "Empty";
                    ErrorMessage = "File is empty";
                    return;
                }

                // Check if file is too large (>100MB)
                if (FileSize > 100 * 1024 * 1024)
                {
                    IsValid = false;
                    Status = "Too Large";
                    ErrorMessage = "File exceeds 100MB limit";
                    return;
                }

                // Check file format support
                if (FileFormat == "Unknown")
                {
                    IsValid = false;
                    Status = "Unsupported";
                    ErrorMessage = "Unsupported file format";
                    return;
                }

                // Try to open file for reading asynchronously
                await Task.Run(() =>
                {
                    using (var stream = File.OpenRead(FilePath))
                    {
                        // File is accessible
                    }
                });

                IsValid = true;
                Status = "Ready";
                ErrorMessage = string.Empty;
            }
            catch (UnauthorizedAccessException)
            {
                IsValid = false;
                Status = "Access Denied";
                ErrorMessage = "Access denied - check file permissions";
            }
            catch (IOException ex)
            {
                IsValid = false;
                Status = "I/O Error";
                ErrorMessage = $"I/O error: {ex.Message}";
            }
            catch (Exception ex)
            {
                IsValid = false;
                Status = "Error";
                ErrorMessage = $"Validation error: {ex.Message}";
            }
        }

        /// <summary>
        /// Creates a default configuration based on the file format
        /// </summary>
        private FileImportConfiguration CreateDefaultConfiguration()
        {
            var config = new FileImportConfiguration();

            // Set format-specific defaults
            switch (FileFormat)
            {
                case "CSV":
                    config.Delimiter = ",";
                    config.HasHeaderRow = true;
                    break;
                case "TXT":
                    config.Delimiter = "\t";
                    config.HasHeaderRow = false;
                    break;
                case "LandXML":
                    // XML files don't use delimiters
                    config.HasHeaderRow = false;
                    break;
                default:
                    config.Delimiter = ",";
                    config.HasHeaderRow = true;
                    break;
            }

            // Try to detect column names from the file (will be done asynchronously)
            _ = DetectColumnNamesAsync(config);

            return config;
        }

        /// <summary>
        /// Attempts to detect column names from the file header asynchronously
        /// </summary>
        private async Task DetectColumnNamesAsync(FileImportConfiguration config)
        {
            try
            {
                bool fileExists = await Task.Run(() => File.Exists(FilePath));
                if (!fileExists)
                    return;

                var firstLine = await Task.Run(() => File.ReadLines(FilePath).FirstOrDefault());
                if (string.IsNullOrEmpty(firstLine))
                    return;

                // Split the first line to get potential column names (fast operation, keep on UI thread)
                var columns = firstLine.Split(new[] { config.Delimiter }, StringSplitOptions.None)
                                      .Select(c => c.Trim().Trim('"'))
                                      .ToArray();

                // Add columns to available list
                config.AvailableColumns.Clear();
                foreach (var column in columns)
                {
                    config.AvailableColumns.Add(column);
                }

                // Try to auto-detect standard column mappings
                AutoDetectColumnMappings(config, columns);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error detecting columns: {ex.Message}");
            }
        }

        /// <summary>
        /// Auto-detects column mappings based on common naming patterns
        /// </summary>
        private void AutoDetectColumnMappings(FileImportConfiguration config, string[] columns)
        {
            foreach (var column in columns)
            {
                var lowerColumn = column.ToLowerInvariant();

                // Point number detection
                if (lowerColumn.Contains("point") || lowerColumn.Contains("number") || lowerColumn.Contains("id"))
                {
                    config.PointNumberColumn = column;
                }
                // Northing detection
                else if (lowerColumn.Contains("north") || lowerColumn.Contains("y"))
                {
                    config.NorthingColumn = column;
                }
                // Easting detection
                else if (lowerColumn.Contains("east") || lowerColumn.Contains("x"))
                {
                    config.EastingColumn = column;
                }
                // Elevation detection
                else if (lowerColumn.Contains("elev") || lowerColumn.Contains("z") || lowerColumn.Contains("height"))
                {
                    config.ElevationColumn = column;
                }
                // Description detection
                else if (lowerColumn.Contains("desc") || lowerColumn.Contains("comment") || lowerColumn.Contains("note"))
                {
                    config.DescriptionColumn = column;
                }
            }
        }

        private static string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
            return $"{bytes / (1024 * 1024 * 1024):F1} GB";
        }

        /// <summary>
        /// Re-detects column names from the file asynchronously
        /// </summary>
        public async Task DetectColumnsAsync()
        {
            await DetectColumnNamesAsync(ImportConfiguration);
        }

        /// <summary>
        /// Re-detects column names from the file (synchronous wrapper for compatibility)
        /// </summary>
        public void DetectColumns()
        {
            _ = DetectColumnsAsync();
        }

        public override string ToString()
        {
            return $"{FileName} ({FileSizeText})";
        }
    }
}