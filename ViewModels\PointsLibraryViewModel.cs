using SurveyPointsManager.Infrastructure.Base;
using SurveyPointsManager.Infrastructure.Commands;
using SurveyPointsManager.Infrastructure.Database;
using System.Windows;
using System.Windows.Input;

namespace SurveyPointsManager.ViewModels
{
    public class PointsLibraryViewModel : ViewModelBase
    {
        private string _title = "Points Library";
        
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public ICommand TestDatabaseCommand { get; }

        public PointsLibraryViewModel()
        {
            TestDatabaseCommand = new RelayCommand(TestDatabaseConnection);
        }

        private void TestDatabaseConnection(object obj)
        {
            try
            {
                var dbService = new DatabaseService();
                dbService.InitializeDatabase();
                MessageBox.Show($"Database connection successful.\n\nDatabase file located at:\n{dbService.GetDbPath()}",
                                "Database Test",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"Database connection failed.\n\nError: {ex.Message}",
                                "Database Test",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
            }
        }
    }
} 