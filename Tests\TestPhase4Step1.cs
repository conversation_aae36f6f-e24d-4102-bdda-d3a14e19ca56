using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using SurveyPointsManager.Infrastructure.Parsers;
using SurveyPointsManager.Infrastructure.Services;
using SurveyPointsManager.ViewModels;

namespace SurveyPointsManager.Tests
{
    /// <summary>
    /// Test for Phase 4 Step 1: File Parser Infrastructure
    /// </summary>
    class TestPhase4Step1
    {
        static async Task Main()
        {
            Console.WriteLine("=== PHASE 4 STEP 1: FILE PARSER INFRASTRUCTURE TEST ===");
            Console.WriteLine("Testing the new file parsing and background processing system");
            Console.WriteLine();
            
            try
            {
                // Test 1: File Parser Factory
                await TestFileParserFactory();
                
                // Test 2: CSV Parser
                await TestCsvParser();
                
                // Test 3: File Processing Service
                await TestFileProcessingService();
                
                // Test 4: Integration Test
                await TestIntegration();
                
                Console.WriteLine("\n🎉 PHASE 4 STEP 1 COMPLETE! 🎉");
                Console.WriteLine("✅ File parser infrastructure working");
                Console.WriteLine("✅ CSV/TXT parsing implemented");
                Console.WriteLine("✅ Background processing service ready");
                Console.WriteLine("✅ Progress reporting functional");
                Console.WriteLine("✅ Error handling comprehensive");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR in Phase 4 Step 1: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        private static async Task TestFileParserFactory()
        {
            Console.WriteLine("🔍 Testing File Parser Factory...");
            
            var factory = new FileParserFactory();
            
            // Test supported formats
            var supportedFormats = factory.GetSupportedFormats();
            Console.WriteLine($"  Supported formats: {string.Join(", ", supportedFormats)}");
            
            // Test parser retrieval
            var csvParser = factory.GetParser("test.csv", "CSV");
            var txtParser = factory.GetParser("test.txt", "TXT");
            var xmlParser = factory.GetParser("test.xml", "LandXML");
            
            Console.WriteLine($"  CSV Parser available: {csvParser != null}");
            Console.WriteLine($"  TXT Parser available: {txtParser != null}");
            Console.WriteLine($"  LandXML Parser available: {xmlParser != null}");
            
            if (csvParser != null && txtParser != null && xmlParser != null)
            {
                Console.WriteLine("  ✅ File parser factory working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ File parser factory test failed");
            }
            Console.WriteLine();
        }
        
        private static async Task TestCsvParser()
        {
            Console.WriteLine("🔍 Testing CSV Parser...");
            
            // Create test CSV file
            string testFile = Path.GetTempFileName();
            string csvContent = @"Point,Northing,Easting,Elevation,Description
101,1000.00,2000.00,150.50,Control Point
102,1100.00,2100.00,155.25,Property Corner
103,1200.00,2200.00,160.00,Utility Pole
104,1300.00,2300.00,165.75,Tree
105,1400.00,2400.00,170.50,Building Corner";
            
            try
            {
                File.WriteAllText(testFile, csvContent);
                
                var parser = new CsvTextParser();
                var configuration = new FileImportConfiguration
                {
                    Delimiter = ",",
                    HasHeaderRow = true,
                    SkipRows = 0,
                    PointNumberColumn = "Point",
                    NorthingColumn = "Northing",
                    EastingColumn = "Easting",
                    ElevationColumn = "Elevation",
                    DescriptionColumn = "Description"
                };
                
                // Set up available columns
                configuration.AvailableColumns.Clear();
                configuration.AvailableColumns.Add("Point");
                configuration.AvailableColumns.Add("Northing");
                configuration.AvailableColumns.Add("Easting");
                configuration.AvailableColumns.Add("Elevation");
                configuration.AvailableColumns.Add("Description");
                
                // Test validation
                var validationResult = await parser.ValidateAsync(testFile, configuration);
                Console.WriteLine($"  File validation: {(validationResult.IsValid ? "PASS" : "FAIL")}");
                Console.WriteLine($"  Estimated points: {validationResult.EstimatedPointCount}");
                Console.WriteLine($"  Detected columns: {validationResult.DetectedColumns.Count}");
                
                // Test parsing with progress
                var progress = new Progress<ParseProgress>(p =>
                {
                    if (p.ProcessedLines % 2 == 0) // Report every other line to avoid spam
                    {
                        Console.WriteLine($"    Progress: {p.PercentComplete:F1}% - {p.CurrentOperation}");
                    }
                });
                
                var parseResult = await parser.ParseAsync(testFile, configuration, progress, CancellationToken.None);
                
                Console.WriteLine($"  Parse success: {parseResult.Success}");
                Console.WriteLine($"  Points parsed: {parseResult.Points.Count}");
                Console.WriteLine($"  Errors: {parseResult.Errors.Count}");
                Console.WriteLine($"  Processing time: {parseResult.Statistics.ProcessingTime.TotalMilliseconds:F0}ms");
                
                if (parseResult.Success && parseResult.Points.Count == 5)
                {
                    Console.WriteLine("  ✅ CSV parser working correctly");
                    
                    // Test first point
                    var firstPoint = parseResult.Points[0];
                    Console.WriteLine($"    First point: {firstPoint.PointNumber} ({firstPoint.Northing}, {firstPoint.Easting}, {firstPoint.Elevation})");
                }
                else
                {
                    Console.WriteLine("  ❌ CSV parser test failed");
                    if (parseResult.Errors.Count > 0)
                    {
                        Console.WriteLine($"    Error: {parseResult.Errors[0].ErrorMessage}");
                    }
                }
            }
            finally
            {
                if (File.Exists(testFile))
                    File.Delete(testFile);
            }
            Console.WriteLine();
        }
        
        private static async Task TestFileProcessingService()
        {
            Console.WriteLine("🔍 Testing File Processing Service...");
            
            var service = new FileProcessingService();
            bool progressReported = false;
            bool completionReported = false;
            
            // Subscribe to events
            service.ProgressChanged += (sender, e) =>
            {
                progressReported = true;
                Console.WriteLine($"    Progress: {e.Progress.OverallProgress:F1}% - {e.Progress.CurrentOperation}");
            };
            
            service.ProcessingCompleted += (sender, e) =>
            {
                completionReported = true;
                Console.WriteLine($"    Completed: Success={e.Success}, Points={e.Points.Count}");
            };
            
            Console.WriteLine($"  Service initialized: {service != null}");
            Console.WriteLine($"  Initial processing state: {service.IsProcessing}");
            
            // Test with empty file list
            var emptyStagedFiles = new StagedFile[0];
            await service.ProcessFilesAsync(emptyStagedFiles, ImportViewModel.ImportModeType.Replace);
            
            Console.WriteLine($"  Progress event fired: {progressReported}");
            Console.WriteLine($"  Completion event fired: {completionReported}");
            Console.WriteLine($"  Final processing state: {service.IsProcessing}");
            
            if (progressReported && completionReported && !service.IsProcessing)
            {
                Console.WriteLine("  ✅ File processing service working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ File processing service test failed");
            }
            Console.WriteLine();
        }
        
        private static async Task TestIntegration()
        {
            Console.WriteLine("🔍 Testing Integration...");
            
            // Create test file
            string testFile = Path.GetTempFileName();
            string csvContent = @"Point,X,Y,Z
201,2000,3000,200
202,2100,3100,205";
            
            try
            {
                File.WriteAllText(testFile, csvContent);
                
                // Create staged file
                var stagedFile = new StagedFile(testFile);
                stagedFile.ImportConfiguration.Delimiter = ",";
                stagedFile.ImportConfiguration.HasHeaderRow = true;
                stagedFile.ImportConfiguration.PointNumberColumn = "Point";
                stagedFile.ImportConfiguration.NorthingColumn = "Y";
                stagedFile.ImportConfiguration.EastingColumn = "X";
                stagedFile.ImportConfiguration.ElevationColumn = "Z";
                
                // Set up available columns
                stagedFile.ImportConfiguration.AvailableColumns.Clear();
                stagedFile.ImportConfiguration.AvailableColumns.Add("Point");
                stagedFile.ImportConfiguration.AvailableColumns.Add("X");
                stagedFile.ImportConfiguration.AvailableColumns.Add("Y");
                stagedFile.ImportConfiguration.AvailableColumns.Add("Z");
                
                // Process through service
                var service = new FileProcessingService();
                var stagedFiles = new[] { stagedFile };
                
                bool success = false;
                int pointCount = 0;
                
                service.ProcessingCompleted += (sender, e) =>
                {
                    success = e.Success;
                    pointCount = e.Points.Count;
                };
                
                await service.ProcessFilesAsync(stagedFiles, ImportViewModel.ImportModeType.Replace);
                
                Console.WriteLine($"  Integration success: {success}");
                Console.WriteLine($"  Points processed: {pointCount}");
                
                if (success && pointCount == 2)
                {
                    Console.WriteLine("  ✅ Integration test passed");
                }
                else
                {
                    Console.WriteLine("  ❌ Integration test failed");
                }
            }
            finally
            {
                if (File.Exists(testFile))
                    File.Delete(testFile);
            }
            Console.WriteLine();
        }
    }
}
