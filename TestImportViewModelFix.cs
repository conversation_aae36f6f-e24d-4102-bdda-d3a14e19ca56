using System;
using SurveyPointsManager.ViewModels;

// Quick test to verify the ImportViewModel naming conflict fix
class TestImportViewModelFix
{
    static void Main()
    {
        Console.WriteLine("=== Testing ImportViewModel Fix ===");
        
        try
        {
            // Create an instance of ImportViewModel
            var viewModel = new ImportViewModel();
            
            Console.WriteLine($"Initial Import Mode: {viewModel.ImportMode}");
            Console.WriteLine($"Is Replace Mode: {viewModel.IsReplaceMode}");
            Console.WriteLine($"Is Append Mode: {viewModel.IsAppendMode}");
            
            // Test switching modes
            viewModel.IsAppendMode = true;
            Console.WriteLine($"After setting Append Mode: {viewModel.ImportMode}");
            Console.WriteLine($"Is Append Mode: {viewModel.IsAppendMode}");
            
            viewModel.IsReplaceMode = true;
            Console.WriteLine($"After setting Replace Mode: {viewModel.ImportMode}");
            Console.WriteLine($"Is Replace Mode: {viewModel.IsReplaceMode}");
            
            // Test enum values directly
            Console.WriteLine($"Replace enum value: {ImportViewModel.ImportModeType.Replace}");
            Console.WriteLine($"Append enum value: {ImportViewModel.ImportModeType.Append}");
            
            Console.WriteLine("\n✅ SUCCESS: ImportViewModel naming conflict has been resolved!");
            Console.WriteLine("The enum 'ImportModeType' and property 'ImportMode' work correctly together.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ ERROR: {ex.Message}");
        }
    }
}
