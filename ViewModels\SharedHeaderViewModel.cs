using System.Windows.Input;
using SurveyPointsManager.Infrastructure.Base;
using SurveyPointsManager.Infrastructure.Commands;
using SurveyPointsManager.Infrastructure.Helpers;

namespace SurveyPointsManager.ViewModels
{
    public class SharedHeaderViewModel : ViewModelBase
    {
        private int _activeTabIndex;

        public bool IsMainTabActive => _activeTabIndex == PaletteSetHelper.TAB_MAIN;
        public bool IsImportTabActive => _activeTabIndex == PaletteSetHelper.TAB_IMPORT;
        public bool IsExportTabActive => _activeTabIndex == PaletteSetHelper.TAB_EXPORT;
        public bool IsPointsTabActive => _activeTabIndex == PaletteSetHelper.TAB_POINTS;
        public bool IsHistoryTabActive => _activeTabIndex == PaletteSetHelper.TAB_HISTORY;
        public bool IsSettingsTabActive => _activeTabIndex == PaletteSetHelper.TAB_SETTINGS;

        public ICommand NavigateToMainCommand { get; }
        public ICommand NavigateToImportCommand { get; }
        public ICommand NavigateToExportCommand { get; }
        public ICommand NavigateToSettingsCommand { get; }
        public ICommand NavigateToHistoryCommand { get; }
        public ICommand NavigateToPointsLibraryCommand { get; }

        public SharedHeaderViewModel()
        {
            // Initialize navigation commands
            NavigateToMainCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateMainTab());
            NavigateToImportCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateImportTab());
            NavigateToExportCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateExportTab());
            NavigateToSettingsCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateSettingsTab());
            NavigateToHistoryCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateHistoryTab());
            NavigateToPointsLibraryCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivatePointsTab());

            // Subscribe to tab changes
            PaletteSetHelper.ActiveTabChanged += (s, tabIndex) =>
            {
                _activeTabIndex = tabIndex;
                OnPropertyChanged(nameof(IsMainTabActive));
                OnPropertyChanged(nameof(IsImportTabActive));
                OnPropertyChanged(nameof(IsExportTabActive));
                OnPropertyChanged(nameof(IsPointsTabActive));
                OnPropertyChanged(nameof(IsHistoryTabActive));
                OnPropertyChanged(nameof(IsSettingsTabActive));
            };

            // Initialize active tab
            _activeTabIndex = PaletteSetHelper.GetActiveTabIndex();
        }
    }
} 