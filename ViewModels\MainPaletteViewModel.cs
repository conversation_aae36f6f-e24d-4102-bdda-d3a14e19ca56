using System.Windows.Input;
using SurveyPointsManager.Infrastructure.Base;
using SurveyPointsManager.Infrastructure.Commands;
using SurveyPointsManager.Infrastructure.Helpers;

namespace SurveyPointsManager.ViewModels
{
    public class MainPaletteViewModel : ViewModelBase
    {
        private string _title = "Survey Points Manager";

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        // Navigation Commands - these will activate the appropriate tabs
        public ICommand NavigateToMainCommand { get; }
        public ICommand NavigateToImportCommand { get; }
        public ICommand NavigateToExportCommand { get; }
        public ICommand NavigateToSettingsCommand { get; }
        public ICommand NavigateToHistoryCommand { get; }
        public ICommand NavigateToPointsLibraryCommand { get; }

        public MainPaletteViewModel()
        {
            // Initialize navigation commands to activate tabs
            NavigateToMainCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateMainTab());
            NavigateToImportCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateImportTab());
            NavigateToExportCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateExportTab());
            NavigateToSettingsCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateSettingsTab());
            NavigateToHistoryCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivateHistoryTab());
            NavigateToPointsLibraryCommand = new RelayCommand((parameter) => PaletteSetHelper.ActivatePointsTab());
        }
    }
} 